{
  // Link: https://blog.logrocket.com/publishing-node-modules-typescript-es-modules/
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@antscorp/antsomi-ui/es": ["./src"],
      "@antscorp/antsomi-ui/es/*": ["./src/*"]
    },
    "target": "ES2020",
    "module": "ESNext",
    "strict": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "noImplicitAny": false,
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    // "jsxFactory": "React.createElement",
    // "jsxFragmentFactory": "React.Fragment",
    "outDir": "./es"

    // "lib": ["dom", "es2017"]
  },
  "include": ["src"],
  "exclude": [
    "node_modules",
    "lib",
    "es",
    "src/**/*.stories.tsx",
    "src/**/*.stories.ts",
    "src/**/*.test.tsx",
    "src/**/*.test.ts",
    "src/stories"
  ]
}
