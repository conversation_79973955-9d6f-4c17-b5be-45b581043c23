// Constants
import { ENV } from '../config';
import { APP_CODES } from './variables';

export const API_RESPONSE_CODE = {
  SUCCESS: 200,
};

const { DATAFLOWS, APP_ANTALYSER } = APP_CODES;

export const TINYMCE_API_KEY = 'scyw71pj8619analvxs56ppc2w2fj2kpy5vnmflhhc300y35';

export const CDP_API = {
  [ENV.DEV]: 'https://sandbox-app.cdp.asia',
  [ENV.SANDBOX]: 'https://sandbox-app.cdp.asia',
  [ENV.SANDBOX_CDP]: 'https://sandbox-app.cdp.asia',
  [ENV.STAGING]: 'https://staging-cdp.antsomi.com',
  [ENV.PROD]: 'https://cdp.antsomi.com',
  [ENV.PRO_VN]: 'https://ap2-cdp.antsomi.com',
};

export const MEDIA_TEMPLATE_API = {
  [ENV.DEV]: 'https://sandbox-media-template.antsomi.com/cdp',
  [ENV.SANDBOX]: 'https://sandbox-media-template.antsomi.com/cdp',
  [ENV.SANDBOX_CDP]: 'https://sandbox-media-template.antsomi.com/cdp',
  [ENV.STAGING]: 'https://staging-media-template.antsomi.com',
  [ENV.PROD]: 'https://media-template.antsomi.com',
  [ENV.PRO_VN]: 'https://staging-media-template.antsomi.com',
};

export const ANTALYSER_API = {
  [ENV.DEV]: 'https://sandbox-antalyser.antsomi.com',
  [ENV.SANDBOX]: 'https://sandbox-antalyser.antsomi.com',
  [ENV.SANDBOX_CDP]: 'https://sandbox-antalyser.antsomi.com',
  [ENV.STAGING]: 'https://sandbox-antalyser.ants.vn',
  [ENV.PROD]: 'https://sandbox-antalyser.ants.vn',
  [ENV.PRO_VN]: 'https://sandbox-antalyser.ants.vn',
};

export const CDP_ROUTE = {
  [ENV.DEV]: 'https://sandbox-cdp.antsomi.com',
  [ENV.SANDBOX]: 'https://sandbox-cdp.antsomi.com',
  [ENV.SANDBOX_CDP]: 'https://sandbox-cdp.antsomi.com',
  [ENV.STAGING]: 'https://staging-cdp.antsomi.com',
  [ENV.PROD]: 'https://cdp.antsomi.com',
  [ENV.PRO_VN]: 'https://ap2-cdp.antsomi.com',
};

export const ISSUE_API = {
  [ENV.DEV]: 'https://sandbox-issue.antsomi.com',
  [ENV.SANDBOX]: 'https://sandbox-issue.antsomi.com',
  [ENV.SANDBOX_CDP]: 'https://sandbox-issue.antsomi.com',
  [ENV.STAGING]: 'https://issue.antsomi.com',
  [ENV.PROD]: 'https://issue.antsomi.com',
  [ENV.PRO_VN]: 'https://issue.antsomi.com',
};

export const PLATFORM_API = 'https://platform.ants.tech';
export const PERMISSION_API = 'https://permission.antsomi.com';
export const SOCKET_API = 'https://ws.ants.tech';
export const IAM_API = 'https://iam.ants.tech';

const PERMISSION_URLS = {
  [ENV.DEV]: 'https://sandbox-permission.ants.vn',
  [ENV.SANDBOX]: 'https://sandbox-permission.ants.vn',
  [ENV.SANDBOX_CDP]: 'https://sandbox-permission.ants.vn',
  [ENV.STAGING]: 'https://permission.antsomi.com',
  [ENV.PROD]: 'https://permission.antsomi.com',
  [ENV.PRO_VN]: 'https://permission.antsomi.com',
};

export const APP_PERMISSION_DOMAIN = {
  [DATAFLOWS]: PERMISSION_URLS,
  [APP_ANTALYSER]: PERMISSION_URLS,
};

export const PERMISSION_ENV_API = PERMISSION_URLS;

export const APP_IAM_DOMAIN = {
  [DATAFLOWS]: {
    [ENV.DEV]: 'https://sandbox-aacm.ants.vn',
    [ENV.SANDBOX]: 'https://sandbox-aacm.ants.vn',
    [ENV.STAGING]: 'https://iam.ants.tech',
    [ENV.PROD]: 'https://iam.ants.tech',
    [ENV.PRO_VN]: 'https://iam.ants.tech',
  },
  [APP_ANTALYSER]: {
    [ENV.DEV]: 'https://sandbox-aacm.ants.vn',
    [ENV.SANDBOX]: 'https://sandbox-aacm.ants.vn',
    [ENV.STAGING]: 'https://iam.ants.tech',
    [ENV.PROD]: 'https://iam.ants.tech',
    [ENV.PRO_VN]: 'https://iam.ants.tech',
  },
};

/* Data Table Performance */
export const COLUMN_DOMAIN = {
  [ENV.DEV]: 'https://column.ants.tech',
  [ENV.SANDBOX]: 'https://column.ants.tech',
  [ENV.PROD]: 'https://column.ants.tech',
  [ENV.PRO_VN]: 'https://column.ants.tech',
};

export const FILTER_DOMAIN = {
  [ENV.DEV]: 'https://column.ants.tech',
  [ENV.SANDBOX]: 'https://column.ants.tech',
  [ENV.PROD]: 'https://column.ants.tech',
  [ENV.PRO_VN]: 'https://column.ants.tech',
};
