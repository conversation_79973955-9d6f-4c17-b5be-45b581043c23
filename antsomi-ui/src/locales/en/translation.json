{"global": {"minus": "Minus", "plus": "Plus", "replace": "Replace", "search": "Search", "save&apply": "Save & Apply", "apply": "Apply", "removeAll": "Remove All", "selectAll": "Select All", "selected": "Selected", "selectItemsFromList": "Select items from the list", "noResultsMatchesKeyWord": "No result matches for your keyword"}, "form": {"placeholder": {"enterTextHere": "Enter text here"}}, "button": {}, "datePicker": {"startDate": "Start Date", "endDate": "End Date", "dateTypes": {"today": "Today", "firstDayOfWeekMonSun": "First day of this week (Mon - Sun)", "firstDayOfWeekSunSat": "First day of this week (Sun - Sat)", "firstDayOfMonth": "First day of this month", "firstDayOfQuarter": "First day of this quarter", "firstDayOfYear": "First day of this year", "lastDayOfWeekMonSun": "Last day of this week (Mon - Sun)", "lastDayOfWeekSunSat": "Last day of this week (Sun - Sat)", "lastDayOfMonth": "Last day of this month", "lastDayOfQuarter": "Last day of this quarter", "lastDayOfYear": "Last day of this year", "fixed": "Fixed"}, "seconds": "Seconds", "minutes": "Minutes", "hours": "Hours", "days": "Days", "weeks": "Weeks", "months": "Months", "quarters": "Quarters", "years": "Years"}, "edit": {"title": "Edit", "description": ""}, "close": {"title": "Close", "description": ""}, "duplicate": {"title": "Duplicate"}, "delete": {"title": "Delete"}, "messageError": {"createColorProfile": {"message": "Create Color Profile Failed", "description": ""}, "maxLength": {"message": "{{name}} is too long, max length is {{maxLength}} characters", "description": ""}, "promotionPoolDeactivated": {"message": "This promotion pool is deactivated", "description": ""}, "BOArchive": {"message": "This BO is archive", "description": ""}, "BODelete": {"message": "This BO does not exist", "description": ""}, "attributeArchive": {"message": "This attribute is not available", "description": ""}, "attributeDelete": {"message": "This attribute does not exist", "description": ""}, "collectionArchive": {"message": "This collection is not available", "description": ""}, "collectionDisable": {"message": "This collection is disabled", "description": ""}, "collectionDelete": {"message": "This collection does not exist", "description": ""}, "blockError": {"message": "There are some blocks that are failing. Please check again", "description": ""}, "boError": {"message": "Content sources has a problem. Please check again", "description": ""}, "fieldIsRequired": {"message": "This field is required", "description": ""}, "completeSetupInContentSources": {"message": "Please complete the setup in Content sources", "description": ""}, "fieldEmpty": {"message": "This field can't be empty"}, "nameExisted": {"message": "This name has already existed"}, "richMenuSwitchNoExist": {"message": "There must be at least 1 Switch Menu action"}}, "expandEditor": {"title": "Expand editor", "description": ""}, "dynamicContent": {"title": "Dynamic content", "modal": {"error": {"boAttr": "Error getting Business Object Attributes", "customerAttr": "Error getting Customer Attributes", "eventAttr": "Error getting Event Attributes", "eventBySource": "Error getting Event in selected Source", "promotionCodeAttr": "Error getting Promotion Code Attributes", "promotionPools": "Error getting Promotion Pools", "source": "Error getting Sources", "visitorAttr": "Error getting Visitor Attributes", "perName": "This name already existed", "function": "Invalid function"}, "label": {"index": "Index", "contentSource": "Content Source", "personalizationName": "Personalization Name", "function": "Function", "ouputDataType": "Output Data Type", "ouputFormat": "Output Format", "saveTemplate": "Save as template", "attribute": "Attribute", "promotionCodeAttr": "Promotion code attribute", "promotionPools": "Promotion pools", "selectEvent": "Select event", "selectEventAttr": "Select event attribute", "selectSource": "In any source of", "displayFormat": "Display Format", "numberFormated": "Display Format", "formNumberDisplayFormat": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "currencySymbol": "Symbol", "currencyCode": "Code", "compact": "Compact numbers", "decimalPlace": "Decimal Places", "grouping": "Grouping", "decimal": "Decimal", "groupingIconTooltipTitle": "Select a mark to separate between groups of thounsands", "decimalIconTooltipTitle": "Select a mark to separate integer and its decimalds"}, "datimeDisplayFormat": {"dateDF": "Date display format", "timeDF": "Time display format", "short": "Short", "medium": "Medium", "long": "<PERSON>", "use24hour": "Use 24-hour clock"}}, "message": {"confirmDeleteVariable": "Are you sure to delete this Dynamic Text ?", "selectDisableEventAttr": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "dynamicContentType": {"customerAttr": "Customer Attribute", "eventAttr": "Event Attribute", "promotionCode": "Promotion Code", "visitorAttr": "Visitor Attribute", "custom": "Custom"}, "attrDisplayFormat": {"number": "Number", "percentage": "Percentage", "currency": "<PERSON><PERSON><PERSON><PERSON>", "datetime": "Datetime", "rawString": "Raw string"}, "title": {"addDynamicContent": "Add Dynamic Content", "deleteVariable": "Delete Dynamic Text"}}, "sidePanel": {"highlightCheckbox": "Highlight Dynamic Content", "title": "Dynamic Content", "addDynamicContent": "Add dynamic content"}}, "matchesAnySelect": {"noData": "You don't have any value <br /> Click Extend Value to add value"}, "linear": "Linear", "radial": "Radial", "gradientStyle": "Gradient Style", "angle": "<PERSON><PERSON>", "gradientColor": "{{name}} Color", "gradientLocation": "{{name}} Color Location", "firstColor": "First Color", "secondColor": "Second Color", "firstColorLocation": "First Color Location", "secondColorLocation": "Second Color Location", "leftTop": "Left Top", "leftCenter": "Left Center", "leftBottom": "Left Bottom", "rightTop": "Right Top", "rightCenter": "Right Center", "rightBottom": "Right Bottom", "centerTop": "Center Top", "centerCenter": "Center Center", "centerBottom": "Center Bottom", "position": "Position", "addColor": "Add color", "ordinalText": {"first": "First", "second": "Second", "third": "Third", "fourth": "Fourth", "fifth": "Fifth", "sixth": "Sixth", "seventh": "Seventh", "eighth": "Eighth", "ninth": "Ninth", "tenth": "Tenth"}, "cellAction": "Click to add image", "templateListing": {"removeSuccess": "Remove template successfully", "removeFailed": "Remove template failed", "addFailed": "Add template failed", "addSuccess": "Add template successfully", "updateFailed": "Update template failed", "updateSuccess": "Update template successfully"}, "products": {"title": "Products", "description": ""}, "articles": {"title": "Articles", "description": ""}, "selectAField": {"title": "Select a field", "description": ""}, "selectContent": {"title": "Select content", "description": ""}, "orSelectAField": {"title": "or Select a field", "description": ""}, "valueFilterEmpty": {"title": "Value filter empty", "description": ""}, "attrDisabled": {"title": "", "description": "Data-updating of this attribute has been disabled. You still can use the output value of the last time it is processed"}, "interest": {"title": "interest", "description": ""}, "with": {"title": "With"}, "days": {"title": "Days", "label": "Days label", "description": ""}, "by": {"title": "by", "description": ""}, "in": {"title": "in", "description": ""}, "last": {"title": "last", "description": ""}, "to": {"title": "to", "description": ""}, "today": {"title": "Today", "description": ""}, "yesterday": {"title": "Yesterday", "description": ""}, "contentSources": {"title": "Content Sources", "description": "", "errorMessage": {"groupSameName": "Content source groups can't have the same name", "groupNameEmpty": " Group name of Content source can't be empty"}}, "fallback": {"title": "Fallback", "options": {"none": "None", "hidden": "Hidden", "mostSeen": "Most seen", "mostCart": "Most cart", "mostBought": "Most bought"}}, "group": {"title": "Group", "description": ""}, "addGroup": {"title": "Add Group", "description": ""}, "groupName": {"title": "Group Name", "description": ""}, "selectAnItem": {"title": "Select an item", "description": ""}, "selectContentSource": {"title": "Select Content source", "description": ""}, "contentSource": {"title": "Content Source"}, "customCode": {"title": "Custom code", "description": ""}, "ranking": {"title": "Ranking", "description": ""}, "addAlgorithms": {"title": "Add Algorithms", "description": ""}, "selectCollection": {"title": "Select collection", "description": ""}, "selectCategory": {"title": "Select category", "description": ""}, "selectAlgorithms": {"title": "Select Algorithms", "description": ""}, "multiSelectAlgorithms": {"title": "Multi-select Algorithms", "description": ""}, "cancel": {"title": "Cancel"}, "apply": {"title": "Apply", "description": ""}, "keywords": {"title": "Keywords", "description": ""}, "title": {"title": "Title", "description": ""}, "name": {"title": "Name", "description": ""}, "tags": {"title": "Tags", "description": ""}, "sort": {"title": "Sort", "description": ""}, "order": {"title": "Order", "description": ""}, "sortBy": {"title": "Sort by", "description": ""}, "sortOrder": {"title": "Sort order", "description": ""}, "mix": {"title": "Mix", "description": ""}, "selectEvent": {"title": "Select event", "description": ""}, "inAnySourceOf": {"title": "In any source of", "description": ""}, "selectEventAttribute": {"title": "Select event attribute", "description": ""}, "selectAttribute": {"title": "Select attribute", "description": ""}, "selectAttributeCapitalize": {"title": "Select Attribute", "description": ""}, "thisFieldIsRequired": {"title": "This field is required", "description": ""}, "custom": {"title": "Custom"}, "excluded": {"title": "Excluded", "description": ""}, "or": {"title": "or", "description": ""}, "field": {"title": "Field", "description": ""}, "index": {"title": "Index", "description": ""}, "selectField": {"title": "Select Field", "description": ""}, "selectIndex": {"title": "Select Index", "description": ""}, "altText": {"title": "Alt Text", "description": ""}, "businessObject": {"title": "Business Object", "description": ""}, "businessObjectSettings": {"title": "Business Object Settings", "description": "", "useOfTemplate": "Use Business Object of Template?"}, "level": {"title": "Level", "description": ""}, "filter": {"title": "Filter", "description": "", "addFilter": "Add filter"}, "filters": {"title": "Filters", "description": ""}, "customRanking": {"title": "Custom Ranking", "description": ""}, "inputYourValue": {"title": "Input your value", "description": ""}, "and": {"title": "And", "description": ""}, "selectDate": {"title": "Select date", "description": ""}, "pleaseInputValue": {"title": "Please input value & press Enter", "description": ""}, "fromBo": {"title": "From BO", "description": ""}, "fromCotentSources": {"title": "From Content sources", "description": ""}, "fromEvent": {"title": "From Event", "description": ""}, "selectBo": {"title": "Select BO", "description": ""}, "selectContentSources": {"title": "Select Content source", "description": ""}, "algorithms": {"title": "Algorithms", "description": ""}, "pagination": {"showRow": "Show row"}, "dataTableToolbar": {"toolbarActions": {"search": "Search", "column": "Column", "explore": "Explore", "groupBy": "Group by"}}, "dataTableFilter": {"saveFilterSet": "Save filter set", "replaceFilterSet": {"title": "Replace filter set", "description": "You already have a filter set named {{filterName}}. Do you want to replace it with this filter set?"}, "deleteFilterSet": {"title": "Delete filter set", "description": "Delete saved filter \"{{filterName}}\" ?"}, "filterBy": "Filter {{objectName}} by", "orGoTo": "Or go to"}, "modifyColumn": {"customizeColumns": "Customize Columns", "saveColumnSet": "Save your column set", "columnsSelected": "{{count}} columns(s) selected", "yourColumnSets": "Your column sets", "deleteColumnSet": {"title": "Delete column set", "description": "Delete saved column set \"{{columnName}}\"", "messageSuccess": "Delete column set success", "messageError": "Delete column set error"}, "replaceColumnSet": {"title": "Replace column set", "description": "You already have a column set named {{columnName}}. Do you want to replace it with this column set?"}}, "remove": "Remove", "format": "Format", "viewAll": "View all", "noData": "No data", "extendValue": {"title": "Extend Value", "placeholder": "Input or paste multiple values separated by Enter"}, "leftMenu": {"pageTitle": {"_recommendation": "Recommendation", "_dashboard": "Dashboard", "_marketing": "Marketing", "_content": "Content", "_templates": "Templates", "_profiles": "Profiles", "_data": "Data", "_insights": "Insights", "_settings": "Settings", "_overview": "Overview", "_allChannel": "All Channels", "_allCampaign": "All Campaigns", "_orchestration": "Journey Orchestration", "_coupons": "Coupons", "_shortLinks": "Short Links", "_mediaTemplate": "Media Templates", "_emailTemplate": "Email Templates", "_journeyTactic": "Journey Tactics", "_customers": "Customers", "_visitors": "Visitors", "_segments": "Segments", "_models": "Models", "_dataSchema": "Data Schema", "_dataflows": "Dataflows", "_histories": "Histories", "_analysis": "Analysis", "_report": "Reports", "_dataSources": "Data Sources", "_gallery": "Gallery", "_accounts": "Accounts", "_roles": "Roles", "_dataProtect": "Data Protect", "_general": "General Settings", "_accountSharing": "Account Sharing", "_ticket": "Ticket", "_channelIntegration": "Channel Integration", "_diagrams": "Diagrams", "_eventSources": "Event Sources", "_events": "Events", "_dataObjects": "Data Objects", "_conversion": "Conversion", "_dataViews": "Data Views", "_eventAttributes": "Event Attributes", "_analyticModels": "Analytic Models", "_batchStream": "Batch Stream", "_realtimeStream": "Realtime Stream", "_sources": "Sources", "_destinations": "Destinations", "_upload": "Upload Histories", "_export": "Export Histories", "_sqlWorkspace": "SQL Workspace", "_scheduleQuery": "Schedule Query", "_survey": "Survey", "_monitor": "Monitor", "_projects": "Projects", "_webPersonalize": "Web Personalize", "_schedule": "Schedule", "_explore": "Explore", "_builtIn": "Built in", "_standard": "Standard", "_ipRestriction": "IP Restriction", "_unsubscribers": "Unsubscribers"}}, "layout": {"recommendationText": "Having trouble navigating the new menus? Let's map functions together!"}, "accessDenied": {"title": "Access Denied", "subTitle": "You don’t have permission to access this feature", "description": "Ask for permission or switch to an account with access", "requestAccess": "Request access", "switchAccount": "Switch account"}, "requestAccess": {"placeholder": "Message (optional)", "deniedTitle": "Access Denied", "deniedSubTitle": "Ask for permission or switch to an account with access", "sentTitle": "Request sent", "sentSubTitle": "You’ll receive an email once your request is approved or denied", "requestText": "Request access", "switchAccountText": "Switch account"}}