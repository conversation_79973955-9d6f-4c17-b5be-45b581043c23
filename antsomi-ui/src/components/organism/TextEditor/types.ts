import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Editor } from '@tiptap/core';
import { LinkAttrs } from './extensions/Link';
import { SmartTagAttrs } from './extensions/SmartTag';
import { ORDERED_LIST_STYLE_TYPE, UNORDERED_LIST_STYLE_TYPE } from './constants';

export type HandleSmartTagRef = {
  setSmartTag: (attrs: SmartTagAttrs) => void;
  deleteSmartTag: (id: string) => void;
  updateSmartTagAttrs: (id: string, updatedAttrs: Omit<SmartTagAttrs, 'id'>) => void;
};

export type HandleLinkRef = {
  setLink: (attrs: LinkAttrs) => void;
  deleteLink: (id: string) => void;
  updateLinkAttrs: (id: string, updatedAttrs: Omit<LinkAttrs, 'id'>) => void;
};

export interface TextEditorProviderRefHandler {
  updateColors?: (colors: string[]) => void;
}

export type TextEditorRef = HandleSmartTagRef &
  TextEditorProviderRefHandler &
  HandleLinkRef & {
    blur: (perserveSelection?: boolean) => void;
    style?: CSSStyleDeclaration;
    readonly editor: Editor | null;
  };

export type SmartTagHandler = Partial<{
  edit: (id: string, event: React.MouseEvent) => void;
  setNew: (args: { selectionText: string }) => void;
}>;

export type LinkHandler = Partial<{
  setNew: (args: { selectionText: string }) => void;
  edit: (args: { id: string; selectionText: string }) => void;
}>;

export type FontConfig = {
  fontFamily: { name: string; fallback?: string[] };
  fontWeight: number[];
};

export type Config = Partial<{
  SmartTag: Partial<{
    highlight: boolean;
  }>;
  Link: Partial<{
    highlightDynamic: boolean;
  }>;
  FontFamily: {
    fonts: FontConfig[];
  };
}>;

export type TextStyle = {
  fontFamily: string;
  fontSize: string;
  color: string;
  backgroundColor: string;
  lineHeight: string;
  fontWeight: string;
  letterSpacing: string;
};

export type TextEditorProps = {
  id?: string;
  className?: string;
  style?: React.CSSProperties;
  initialContent?: string;
  config?: Config;
  smartTagHandler?: SmartTagHandler;
  linkHandler?: LinkHandler;
  editable?: boolean;
  bubbleMenuProps?: {
    container?: HTMLElement | null;
  };
  defaultTextStyle?: Partial<TextStyle>;
  dataAttributes?: Record<`data-${string}`, unknown>;
  onUpdateDebounced?: number;
  onFocus?: (event: FocusEvent) => void;
  onUpdate?: (args: { html: string; text: string; json: JSONContent }) => void;
  onCreate?: (args: { html: string; text: string; json: JSONContent }) => void;
  onChangeColorsSet?: (colors: string[]) => void;
};

export type OrderedListStyleType =
  (typeof ORDERED_LIST_STYLE_TYPE)[keyof typeof ORDERED_LIST_STYLE_TYPE];

export type UnorderedListStyleType =
  (typeof UNORDERED_LIST_STYLE_TYPE)[keyof typeof UNORDERED_LIST_STYLE_TYPE];

export const isOrderedListStyleType = (value: unknown): value is OrderedListStyleType =>
  typeof value === 'string' && Object.values(ORDERED_LIST_STYLE_TYPE).some(v => v === value);

export const isUnorderedListStyleType = (value: unknown): value is UnorderedListStyleType =>
  typeof value === 'string' && Object.values(UNORDERED_LIST_STYLE_TYPE).some(v => v === value);
