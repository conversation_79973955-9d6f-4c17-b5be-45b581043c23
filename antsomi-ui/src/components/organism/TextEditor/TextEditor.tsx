import { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef } from 'react';
import { Editor, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { TextStyleKit } from '@tiptap/extension-text-style';
import Superscript from '@tiptap/extension-superscript';
import SubScript from '@tiptap/extension-subscript';
import TextAlign from '@tiptap/extension-text-align';
import { FontSize } from './extensions/FontSize';
import { TextTransform } from './extensions/TextTransform';
import { FontFamily } from './extensions/FontFamily';
import { SmartTag } from './extensions/SmartTag';
import { Indent } from './extensions/Indent';
import { LineHeight } from './extensions/LineHeight';
import { TextEditorRef, TextEditorProps, TextStyle } from './types';
import { Link as LinkExtension } from './extensions/Link';
import { Toolbar } from './ui/Toolbar/Toolbar';
import { EditorView } from '@tiptap/pm/view';
import { EditorState } from '@tiptap/pm/state';
import { Selection } from '@tiptap/extensions';
import {
  defaultShouldShowBubbleMenu,
  handleLinkAction,
  handleSmartTagAction,
  isShowLinkMenu,
  safeParseHTMLContent,
} from './utils';
import { useLinkHandler } from './hooks/useLinkHandler';
import { useDebouncedCallback } from 'use-debounce';
import { StyledBubbleMenu, StyledEditorContent } from './styled';
import { BackgroundColor } from './extensions/BackgroundColor';
import { isBoolean } from 'lodash';
import { DEFAULT_TEXT_STYLE } from './constants';
import { Color } from './extensions/Color';
import { Attrs } from '@tiptap/pm/model';
import { FontWeight } from './extensions/FontWeight';
import { useTextEditorStore } from './provider';
import { useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';
import { Emoji } from './extensions/Emoji';
import { emojiSuggestion } from './ui/Emoji';
import clsx from 'clsx';
import { ANTSOMI_COMPONENT_PREFIX_CLS } from '@antscorp/antsomi-ui/es/constants';
import produce from 'immer';
import { LetterSpacing } from './extensions/LetterSpacing';
import { CustomOrderedList } from './extensions/OrderedList';
import { CustomUnorderedList } from './extensions/UnorderedList';

export const TextEditor = memo(
  forwardRef<TextEditorRef, TextEditorProps>((props, ref) => {
    const {
      id,
      className,
      config,
      editable = true,
      initialContent,
      dataAttributes,
      onUpdateDebounced = 400,
      defaultTextStyle: defaultTextStyleProp,
      linkHandler: outerLinkHandler,
      smartTagHandler: outerSmartTagHandler,
      bubbleMenuProps,
      style,
      onUpdate,
      onFocus,
      onCreate,
    } = props;

    const isShowBubbleMenu = useTextEditorStore(state => state.isShowBubbleMenu);
    const setBubbleMenuContainer = useTextEditorStore(state => state.setBubbleMenuContainer);

    const defaultTextStyle: TextStyle = useDeepCompareMemo(
      () => ({
        ...DEFAULT_TEXT_STYLE,
        ...defaultTextStyleProp,
      }),
      [defaultTextStyleProp],
    );

    const handleOnUpdateDebounce = useDebouncedCallback((editor: Editor) => {
      const html = editor.getHTML();
      const text = editor.getText();
      const json = editor.getJSON();

      onUpdate?.({ html, text, json });
    }, onUpdateDebounced);

    const contentRef = useRef<HTMLDivElement | null>(null);

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          link: false,
          bulletList: false,
          orderedList: false,
          listItem: {
            HTMLAttributes: {
              style: 'line-height:normal;',
            },
          },
          undoRedo: {
            depth: 100,
          },
        }),

        CustomUnorderedList,
        CustomOrderedList,

        FontWeight.configure({
          defaultWeight: defaultTextStyle.fontWeight,
        }),
        LineHeight.configure({
          defaultHeight: defaultTextStyle.lineHeight,
        }),
        FontFamily.configure({
          defaultFontFamily: defaultTextStyle.fontFamily,
        }),
        FontSize.configure({
          defaultFontSize: defaultTextStyle.fontSize,
        }),
        LetterSpacing.configure({
          defaultLetterSpacing: defaultTextStyle.letterSpacing,
        }),
        Color.configure({
          defaultColor: defaultTextStyle.color,
        }),
        BackgroundColor.configure({
          defaultBackgroundColor: defaultTextStyle.backgroundColor,
        }),
        TextStyleKit.configure({
          backgroundColor: false,
          color: false,
          textStyle: {
            mergeNestedSpanStyles: true,
          },
        }),
        TextTransform,
        Superscript,
        SubScript,
        Selection.configure({
          className: 'selection',
        }),
        SmartTag.configure({
          highlight: config?.SmartTag?.highlight,
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        LinkExtension.configure({
          openOnClick: false,
          HTMLAttributes: {
            class: 'link',
            style: 'text-decoration:inherit;color:inherit;',
            rel: undefined,
          },
        }),
        Indent,
        Emoji.configure({
          suggestion: emojiSuggestion({
            container: bubbleMenuProps?.container,
          }),
        }),
      ],
      content: safeParseHTMLContent(initialContent || ''),
      onCreate: ({ editor }) => {
        const html = editor.getHTML();
        const text = editor.getText();
        const json = editor.getJSON();

        onCreate?.({ html, text, json });
      },
      onUpdate: ({ editor }) => {
        handleOnUpdateDebounce(editor);
      },
      onFocus: ({ event }) => {
        onFocus?.(event);
      },
      editorProps: {
        attributes: { spellcheck: 'false' },
        handleKeyDown(view: EditorView, event) {
          if (event.ctrlKey && event.key === 'k') {
            // Add this line to prevent browser's default behavior.
            event.preventDefault();

            handleLinkAction(view, outerLinkHandler);
          }
        },
      },
    });

    const { setLink, deleteLink, updateLinkAttrsGlobally } = useLinkHandler({
      editor,
    });

    // useStyleMemory(editor);

    useEffect(() => {
      if (editable === undefined) {
        editor?.setEditable(true);
      } else if (isBoolean(editable)) {
        editor?.setEditable(editable);
      }
    }, [editor, editable]);

    useEffect(() => {
      if (editor && config?.SmartTag?.highlight !== undefined) {
        editor.chain().updateSmartTagHighlight(config.SmartTag.highlight).run();
      }
    }, [editor, config?.SmartTag?.highlight]);

    useEffect(() => {
      if (!isBoolean(config?.Link?.highlightDynamic)) {
        return;
      }

      updateLinkAttrsGlobally(attrs =>
        produce(attrs, draft => {
          if (!attrs.dynamic) return;

          const updatedClass = new Set(String(attrs.class).split(' '));

          if (config?.Link?.highlightDynamic) {
            updatedClass.add('highlight');
          } else {
            updatedClass.delete('highlight');
          }

          draft.class = [...updatedClass].join(' ');
        }),
      );
    }, [config?.Link?.highlightDynamic, updateLinkAttrsGlobally]);

    const handleSetSmartTag = useCallback<TextEditorRef['setSmartTag']>(
      ({ id, content }) => {
        editor?.chain().focus().setSmartTag({ id, content }).run();
      },
      [editor],
    );

    const handleDeleteSmartTag = useCallback<TextEditorRef['deleteSmartTag']>(
      id => {
        editor
          ?.chain()
          .deleteSmartTagGlobally(attrs => attrs.id === id)
          .run();
      },
      [editor],
    );

    const handleUpdateSmartTagAttrs = useCallback<TextEditorRef['updateSmartTagAttrs']>(
      (id, updatedAttrs) =>
        editor
          ?.chain()
          .updateSmartTagAttrsGlobally(attrs => {
            if (attrs.id === id) return updatedAttrs;
          })
          .run(),
      [editor],
    );

    const handleUpdateLinkAttrs = useCallback<TextEditorRef['updateLinkAttrs']>(
      (id, updatedAttrs) =>
        updateLinkAttrsGlobally((attrs: Attrs) => {
          if (attrs.id === id) {
            return updatedAttrs;
          }

          if (!attrs.id && attrs.href === updatedAttrs.href) {
            return { id, ...updatedAttrs };
          }
        }),
      [updateLinkAttrsGlobally],
    );

    const shouldShowBubbleMenu = useCallback(
      (params: { element: HTMLElement; state: EditorState; view: EditorView; editor: Editor }) => {
        const { state, view, element } = params;

        if (isShowLinkMenu(state)) {
          return true;
        }

        return defaultShouldShowBubbleMenu({
          state,
          view,
          element,
          editor: params.editor,
        });
      },
      [],
    );

    const handleBlur = useCallback(
      (perserveSelection?: boolean) => {
        if (perserveSelection) {
          editor?.chain().blur().run();
        } else {
          editor?.chain().blur().setTextSelection({ from: 0, to: 0 }).run();
        }
      },
      [editor],
    );

    const handleBubbleMenuRef = useCallback(
      (htmlElement: HTMLDivElement | null) => {
        setBubbleMenuContainer(htmlElement);
      },
      [setBubbleMenuContainer],
    );

    useImperativeHandle(ref, () => ({
      setLink,
      deleteLink,
      updateLinkAttrs: handleUpdateLinkAttrs,
      setSmartTag: handleSetSmartTag,
      deleteSmartTag: handleDeleteSmartTag,
      updateSmartTagAttrs: handleUpdateSmartTagAttrs,
      blur: handleBlur,
      style: contentRef.current?.style,
      editor,
    }));

    if (!editor) {
      return null;
    }

    return (
      <>
        <StyledEditorContent
          className={clsx(`${ANTSOMI_COMPONENT_PREFIX_CLS}-text-editor`, className)}
          $textStyle={defaultTextStyle}
          style={{
            // Inline styles apply to inner html elements (support email client)
            ...defaultTextStyle,
            ...style,
          }}
          id={id}
          ref={contentRef}
          editor={editor}
          {...dataAttributes}
        />

        <StyledBubbleMenu
          ref={handleBubbleMenuRef}
          editor={editor}
          shouldShow={shouldShowBubbleMenu}
          resizeDelay={700}
          {...bubbleMenuProps}
        >
          {isShowBubbleMenu && (
            <Toolbar
              config={config}
              editor={editor}
              defaultTextStyle={defaultTextStyle}
              linkHanlder={{
                onUpsert: () => {
                  handleLinkAction(editor.view, outerLinkHandler);
                },
              }}
              smartTagHandler={{
                onUpsert: (event: React.MouseEvent) => {
                  handleSmartTagAction(event, editor.view, outerSmartTagHandler);
                },
              }}
            />
          )}
        </StyledBubbleMenu>
      </>
    );
  }),
);
