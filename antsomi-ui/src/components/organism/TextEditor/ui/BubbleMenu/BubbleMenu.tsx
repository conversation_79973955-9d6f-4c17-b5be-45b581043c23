/* eslint-disable no-console */
import { useCurrentEditor } from '@tiptap/react';
import React, { forwardRef, useRef } from 'react';
import { createPortal } from 'react-dom';
import { BubbleMenuPlugin, BubbleMenuPluginProps } from '../../extensions/BubbleMenu';
import { useDeepCompareEffect, useIsMounted } from '@antscorp/antsomi-ui/es/hooks';
import { useTextEditorStore } from '../../provider';
import clsx from 'clsx';
import { ANTSOMI_COMPONENT_PREFIX_CLS } from '@antscorp/antsomi-ui/es/constants';

type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;

export type BubbleMenuProps = Optional<
  Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element'>,
  'editor'
> &
  React.HTMLAttributes<HTMLDivElement>;

export const BubbleMenu = forwardRef<HTMLDivElement, BubbleMenuProps>(
  (
    {
      pluginKey = 'bubbleMenu',
      editor,
      updateDelay,
      resizeDelay,
      shouldShow = null,
      options,
      children,
      container,
      className,
      ...restProps
    },
    ref,
  ) => {
    const menuEl = useRef(document.createElement('div'));

    const isMounted = useIsMounted();

    const setIsShowBubbleMenu = useTextEditorStore(state => state.setIsShowBubbleMenu);

    console.log({ setIsShowBubbleMenu });

    if (typeof ref === 'function') {
      ref(menuEl.current);
    } else if (ref) {
      ref.current = menuEl.current;
    }

    const { editor: currentEditor } = useCurrentEditor();

    useDeepCompareEffect(() => {
      const bubbleMenuElement = menuEl.current;

      bubbleMenuElement.className = clsx(
        className,
        `${ANTSOMI_COMPONENT_PREFIX_CLS}-text-editor-bubble-menu`,
      );

      if (editor?.isDestroyed || currentEditor?.isDestroyed) {
        return;
      }

      const attachToEditor = editor || currentEditor;

      if (!attachToEditor) {
        console.warn(
          'BubbleMenu component is not rendered inside of an editor component or does not have editor prop.',
        );
        return;
      }

      const plugin = BubbleMenuPlugin({
        updateDelay,
        resizeDelay,
        editor: attachToEditor,
        element: bubbleMenuElement,
        container,
        pluginKey,
        shouldShow,
        onShow: () => {
          if (isMounted()) {
            setIsShowBubbleMenu(true);
          }
        },
        onHide: () => {
          if (isMounted()) {
            setIsShowBubbleMenu(false);
          }
        },
        options: {
          ...options,
          strategy: options?.strategy || 'absolute',
          placement: options?.placement || 'top-start',
          offset: options?.offset || {
            alignmentAxis: -20,
            mainAxis: 10,
          },
        },
      });

      attachToEditor.registerPlugin(plugin);

      return () => {
        attachToEditor.unregisterPlugin(pluginKey);

        window.requestAnimationFrame(() => {
          if (bubbleMenuElement.parentNode) {
            bubbleMenuElement.parentNode.removeChild(bubbleMenuElement);
          }
        });
      };
    }, [
      editor,
      container,
      updateDelay,
      resizeDelay,
      pluginKey,
      options,
      className,
      currentEditor,
      isMounted,
      shouldShow,
      setIsShowBubbleMenu,
    ]);

    return createPortal(<div {...restProps}>{children}</div>, menuEl.current);
  },
);
