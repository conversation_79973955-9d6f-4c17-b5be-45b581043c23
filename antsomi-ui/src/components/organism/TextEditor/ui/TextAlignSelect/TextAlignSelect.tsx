import { formatHotkey } from '@antscorp/antsomi-ui/es/utils';
import { Editor } from '@tiptap/core';
import { Flex } from 'antd';
import { useMemo, useState } from 'react';
import { TEXT_EDITOR_CONSTANTS } from '../../constants';
import { TextEditorPopover } from '../Popover';
import { TextEditorButton } from '../Button';
import {
  ALignRightIcon,
  AlignCenterIcon,
  AlignJustifyIcon,
  AlignLeftIcon,
  ArrowDropDownIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import styled from 'styled-components';
import { useEditorState } from '@tiptap/react';

const { TEXT_ALIGN } = TEXT_EDITOR_CONSTANTS;

const textAlignOptions = [
  {
    label: TEXT_ALIGN.OPTIONS.LEFT.LABEL,
    value: TEXT_ALIGN.OPTIONS.LEFT.VALUE,
    shortcut: formatHotkey('Shift+L'),
    icon: AlignLeftIcon,
  },
  {
    label: TEXT_ALIGN.OPTIONS.CENTER.LABEL,
    value: TEXT_ALIGN.OPTIONS.CENTER.VALUE,
    shortcut: formatHotkey('Shift+E'),
    icon: AlignCenterIcon,
  },
  {
    label: TEXT_ALIGN.OPTIONS.RIGHT.LABEL,
    value: TEXT_ALIGN.OPTIONS.RIGHT.VALUE,
    shortcut: formatHotkey('Shift+R'),
    icon: ALignRightIcon,
  },
  {
    label: TEXT_ALIGN.OPTIONS.JUSTIFY.LABEL,
    value: TEXT_ALIGN.OPTIONS.JUSTIFY.VALUE,
    shortcut: formatHotkey('Shift+J'),
    icon: AlignJustifyIcon,
  },
] as const;

type AlignType = (typeof textAlignOptions)[number]['value'];

interface TextAlignSelectProps {
  editor?: Editor;
  onChange?: (value: AlignType) => void;
}

const StyledTriggerButton = styled(TextEditorButton)`
  &.antsomi-btn.antsomi-btn-text {
    width: 40px;
    gap: 0;

    &.is-active {
      background-color: unset;
    }
  }
`;

export const TextAlignSelect = (props: TextAlignSelectProps) => {
  const { editor, onChange } = props;

  const [isOpen, setIsOpen] = useState(false);

  const { currentAlignment } = useEditorState({
    editor: editor!,
    selector: ({ editor: editorInstance }: { editor: Editor }) => {
      const alignment = (['left', 'center', 'right', 'justify'] as const).find(alignment =>
        editorInstance?.isActive({ textAlign: alignment }),
      );
      return {
        currentAlignment: alignment || 'left',
      };
    },
  });

  const selectedOption = useMemo(
    () => textAlignOptions.find(option => option.value === currentAlignment),
    [currentAlignment],
  );

  const handleClickOption = (align: AlignType) => {
    setIsOpen(false);

    onChange?.(align);
  };

  let AlignIconComponent = textAlignOptions[0].icon;

  if (selectedOption) {
    AlignIconComponent = selectedOption.icon;
  }

  return (
    <TextEditorPopover
      destroyTooltipOnHide
      open={isOpen}
      arrow={false}
      overlayInnerStyle={{ padding: 8 }}
      content={
        <Flex gap={8}>
          {textAlignOptions.map(option => (
            <TextEditorButton
              key={option.value}
              icon={<option.icon key={option.value} size={16} />}
              onClick={() => handleClickOption(option.value)}
              isActive={currentAlignment === option.value}
              tooltipProps={{
                title: `${TEXT_ALIGN.TOOLTIP} ${option.label.toLowerCase()} (${option.shortcut})`,
              }}
            />
          ))}
        </Flex>
      }
      trigger={['click']}
      placement="bottomLeft"
      onOpenChange={setOpen => setIsOpen(setOpen)}
    >
      <StyledTriggerButton
        icon={<AlignIconComponent size={18} />}
        isActive={!!currentAlignment}
        tooltipProps={{ title: TEXT_ALIGN.TITLE }}
      >
        <ArrowDropDownIcon
          style={{
            flexShrink: 0,
            ...(isOpen && {
              transform: 'rotate(180deg)',
            }),
          }}
          size={16}
        />
      </StyledTriggerButton>
    </TextEditorPopover>
  );
};
