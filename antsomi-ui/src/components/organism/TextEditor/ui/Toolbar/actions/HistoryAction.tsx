import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface HistoryActionProps {
  editor: Editor;
}

const UndoIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M10.1768 9.02857C8.29834 9.37143 6.64088 10.0571 5.09392 11.3143L2 8V16H9.73481L6.75138 12.9143C10.8398 9.94286 16.4751 10.8571 19.4586 15.0857C19.6796 15.4286 19.9006 15.6571 20.011 16L22 14.9714C19.5691 10.6286 14.9282 8.22857 10.1768 9.02857Z"
      fill="currentColor"
    />
  </svg>
);

const RedoIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M13.8232 9.02857C15.7017 9.37143 17.3591 10.0571 18.9061 11.3143L22 8V16H14.2652L17.2486 12.9143C13.1602 9.94286 7.52486 10.8571 4.54144 15.0857C4.32044 15.4286 4.09945 15.6571 3.98895 16L2 14.9714C4.43094 10.6286 9.07182 8.22857 13.8232 9.02857Z"
      fill="currentColor"
    />
  </svg>
);

export const HistoryAction = ({ editor }: HistoryActionProps) => {
  const { disabledUndo, disabledRedo } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabledUndo: !editorInstance.can().chain().focus().undo().run(),
      disabledRedo: !editorInstance.can().chain().focus().redo().run(),
    }),
  });

  return (
    <>
      <TextEditorButton
        disabled={disabledUndo}
        onClick={() => editor.chain().focus().undo().run()}
        icon={<UndoIcon size={18} />}
        tooltipProps={{
          title: `${TOOLTIPS.UNDO.TITLE} (${TOOLTIPS.UNDO.SHORTCUT})`,
        }}
      />

      <TextEditorButton
        disabled={disabledRedo}
        onClick={() => editor.chain().focus().redo().run()}
        icon={<RedoIcon size={18} />}
        tooltipProps={{
          title: `${TOOLTIPS.REDO.TITLE} (${TOOLTIPS.REDO.SHORTCUT})`,
        }}
      />
    </>
  );
};
