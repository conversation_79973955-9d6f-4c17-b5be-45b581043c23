import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface LinkActionProps {
  editor: Editor;
  onClick?: () => void;
}

const LinkIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    height={size}
    width={size}
    viewBox="0 -960 960 960"
    fill="currentColor"
  >
    <path d="M680-160v-120H560v-80h120v-120h80v120h120v80H760v120h-80ZM440-280H280q-83 0-141.5-58.5T80-480q0-83 58.5-141.5T280-680h160v80H280q-50 0-85 35t-35 85q0 50 35 85t85 35h160v80ZM320-440v-80h320v80H320Zm560-40h-80q0-50-35-85t-85-35H520v-80h160q83 0 141.5 58.5T880-480Z" />
  </svg>
);

export const LinkAction = ({ editor, onClick }: LinkActionProps) => {
  const { isActive } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isActive: editorInstance.isActive('link'),
    }),
  });

  return (
    <TextEditorButton
      isActive={isActive}
      icon={<LinkIcon size={18} />}
      onClick={() => onClick?.()}
      tooltipProps={{
        title: TOOLTIPS.LINK.TITLE,
      }}
    />
  );
};
