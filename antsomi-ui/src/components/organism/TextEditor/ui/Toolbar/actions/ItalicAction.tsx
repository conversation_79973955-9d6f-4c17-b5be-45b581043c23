import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface ItalicActionProps {
  editor: Editor;
}

const ItalicIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M3 22V18.4286H8.84615L13.2308 5.57143H7.38462V2H22V5.57143H16.8846L12.5 18.4286H17.6154V22H3Z"
      fill="currentColor"
    />
  </svg>
);

export const ItalicAction = ({ editor }: ItalicActionProps) => {
  const { isItalic, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isItalic: editorInstance.isActive('italic'),
      disabled: !editorInstance.can().chain().focus().toggleItalic().run(),
    }),
  });

  return (
    <TextEditorButton
      onClick={() => editor.chain().focus().toggleItalic().run()}
      disabled={disabled}
      isActive={isItalic}
      icon={<ItalicIcon size={18} />}
      tooltipProps={{
        title: `${TOOLTIPS.ITALIC.TITLE} (${TOOLTIPS.ITALIC.SHORTCUT})`,
      }}
    />
  );
};
