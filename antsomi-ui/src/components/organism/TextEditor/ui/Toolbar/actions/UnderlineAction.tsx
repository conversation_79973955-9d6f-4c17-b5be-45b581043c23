import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface UnderlineActionProps {
  editor: Editor;
}

const UnderlineIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M4 22V19.7778H20V22H4ZM12 17.5556C10.0762 17.5556 8.58095 16.9722 7.51429 15.8056C6.44762 14.6389 5.91429 13.0926 5.91429 11.1667V2H8.85714V11.3333C8.85714 12.3704 9.12381 13.213 9.65714 13.8611C10.1905 14.5093 10.9714 14.8333 12 14.8333C13.0286 14.8333 13.8095 14.5093 14.3429 13.8611C14.8762 13.213 15.1429 12.3704 15.1429 11.3333V2H18.0857V11.1667C18.0857 13.0926 17.5524 14.6389 16.4857 15.8056C15.419 16.9722 13.9238 17.5556 12 17.5556Z"
      fill="currentColor"
    />
  </svg>
);

export const UnderlineAction = ({ editor }: UnderlineActionProps) => {
  const { isUnderline, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isUnderline: editorInstance.isActive('underline'),
      disabled: !editorInstance.can().chain().focus().toggleUnderline().run(),
    }),
  });

  return (
    <TextEditorButton
      onClick={() => editor.chain().focus().toggleUnderline().run()}
      disabled={disabled}
      isActive={isUnderline}
      icon={<UnderlineIcon size={18} />}
      tooltipProps={{
        title: `${TOOLTIPS.UNDERLINE.TITLE} (${TOOLTIPS.UNDERLINE.SHORTCUT})`,
      }}
    />
  );
};
