import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS, UNORDERED_LIST_STYLE_TYPE } from '../../../constants';
import { isUnorderedListStyleType, UnorderedListStyleType } from '../../../types';
import { CustomUnorderedList } from '../../../extensions/UnorderedList';
import { SplitButtonDropdown } from '../../SplitButtonDropdown';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface BulletListActionProps {
  editor: Editor;
}

const UnorderListIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M3.57895 11C2.73684 11 2 11.7 2 12.5C2 13.3 2.73684 14 3.57895 14C4.42105 14 5.15789 13.3 5.15789 12.5C5.15789 11.7 4.42105 11 3.57895 11ZM3.57895 6C2.73684 6 2 6.7 2 7.5C2 8.3 2.73684 9 3.57895 9C4.42105 9 5.15789 8.3 5.15789 7.5C5.15789 6.7 4.42105 6 3.57895 6ZM3.57895 16C2.73684 16 2 16.7 2 17.5C2 18.3 2.73684 19 3.57895 19C4.42105 19 5.15789 18.3 5.15789 17.5C5.15789 16.7 4.42105 16 3.57895 16ZM7.26316 6.5V8.5H22V6.5H7.26316ZM7.26316 18.5H22V16.5H7.26316V18.5ZM7.26316 13.5H22V11.5H7.26316V13.5Z"
      fill="currentColor"
    />
  </svg>
);

const OPTIONS: { label: string; value: UnorderedListStyleType }[] = [
  {
    label: '•',
    value: UNORDERED_LIST_STYLE_TYPE.disc,
  },
  {
    label: '○',
    value: UNORDERED_LIST_STYLE_TYPE.circle,
  },
  {
    label: '■',
    value: UNORDERED_LIST_STYLE_TYPE.square,
  },
  {
    label: 'None',
    value: UNORDERED_LIST_STYLE_TYPE.none,
  },
];

export const BulletListAction = ({ editor }: BulletListActionProps) => {
  const { value, disabled, isSelected } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().toggleCustomUnorderedList().run(),
      value: editorInstance.getAttributes(CustomUnorderedList.name).listStyleType,
      isSelected: editorInstance.isActive(CustomUnorderedList.name),
    }),
  });

  return (
    <SplitButtonDropdown
      disabled={disabled}
      value={value}
      options={OPTIONS}
      onClickOption={v => {
        if (!isUnorderedListStyleType(v)) return;

        if (isSelected) {
          editor.chain().focus().setUnorderedListStyle(v).run();
          return;
        }

        editor
          .chain()
          .focus()
          .toggleCustomUnorderedList({
            listStyleType: v,
          })
          .run();
      }}
      onClick={() => {
        editor.chain().focus().toggleCustomUnorderedList().run();
      }}
      tooltipProps={{
        title: `${TOOLTIPS.BULLET_LIST.TITLE} (${TOOLTIPS.BULLET_LIST.SHORTCUT})`,
      }}
    >
      <UnorderListIcon size={18} />
    </SplitButtonDropdown>
  );
};
