import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface BoldActionProps {
  editor: Editor;
}

const BoldIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M5 22V2H12.9687C14.5312 2 15.9736 2.47619 17.2957 3.42857C18.6178 4.38095 19.2788 5.70238 19.2788 7.39286C19.2788 8.60714 19.0024 9.54167 18.4495 10.1964C17.8966 10.8512 17.3798 11.3214 16.899 11.6071C17.5 11.869 18.1671 12.3571 18.9002 13.0714C19.6334 13.7857 20 14.8571 20 16.2857C20 18.4048 19.2187 19.8869 17.6562 20.7321C16.0937 21.5774 14.6274 22 13.2572 22H5ZM9.36298 18H13.113C14.2668 18 14.97 17.7083 15.2224 17.125C15.4748 16.5417 15.601 16.119 15.601 15.8571C15.601 15.5952 15.4748 15.1726 15.2224 14.5893C14.97 14.006 14.2308 13.7143 13.0048 13.7143H9.36298V18ZM9.36298 9.85714H12.7163C13.5096 9.85714 14.0865 9.65476 14.4471 9.25C14.8077 8.84524 14.988 8.39286 14.988 7.89286C14.988 7.32143 14.7837 6.85714 14.375 6.5C13.9663 6.14286 13.4375 5.96429 12.7885 5.96429H9.36298V9.85714Z"
      fill="currentColor"
    />
  </svg>
);

export const BoldAction = ({ editor }: BoldActionProps) => {
  const { isBold, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isBold: editorInstance.isActive('bold'),
      disabled: !editorInstance.can().chain().focus().toggleBold().run(),
    }),
  });

  return (
    <TextEditorButton
      disabled={disabled}
      onClick={() => editor.chain().focus().toggleBold().run()}
      isActive={isBold}
      icon={<BoldIcon size={18} />}
      tooltipProps={{
        title: `${TOOLTIPS.BOLD.TITLE} (${TOOLTIPS.BOLD.SHORTCUT})`,
      }}
    />
  );
};
