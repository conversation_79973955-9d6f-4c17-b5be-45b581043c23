import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { DEFAULT_TEXT_STYLE, TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface ClearFormattingActionProps {
  editor: Editor;
}

const RemoveFormattingIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M3.41111 3L2 4.42875L9.74445 12.27L7 18.75H10.3333L12.0778 14.6325L18.3667 21L19.7778 19.5712L3.72222 3.30375L3.41111 3ZM6.44444 3V3.2025L9.57778 6.375H12.2444L11.4444 8.265L13.7778 10.6275L15.5667 6.375H22V3H6.44444Z"
      fill="currentColor"
    />
  </svg>
);

export const ClearFormattingAction = ({ editor }: ClearFormattingActionProps) => {
  const { disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().run(),
    }),
  });

  const clearFormatting = () => {
    editor
      ?.chain()
      .updateAttributes('textStyle', DEFAULT_TEXT_STYLE)
      .unsetAllMarks()
      .clearNodes()
      .unsetTextAlign()
      .unsetTextTransform()
      .run();
  };

  return (
    <TextEditorButton
      onClick={clearFormatting}
      disabled={disabled}
      icon={<RemoveFormattingIcon size={18} />}
      tooltipProps={{
        title: TOOLTIPS.CLEAR_FORMATTING.TITLE,
      }}
    />
  );
};
