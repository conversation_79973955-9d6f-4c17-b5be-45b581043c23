import { Editor } from '@tiptap/core';
import { useTextEditorStore } from '../../../provider';
import {
  FontSizeInput,
  FontSizeInputHandlerRef,
} from '@antscorp/antsomi-ui/es/components/molecules';
import styled from 'styled-components';
import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { useThrottledCallback } from 'use-debounce';
import { DEFAULT_TEXT_STYLE } from '../../../constants';
import { useEditorState } from '@tiptap/react';

export interface FontSizeActionProps {
  editor: Editor;
  throttled?: number;
}

const StyledFontSizeInput = styled(FontSizeInput)`
  &.antsomi-input-number {
    width: 36px;

    input.antsomi-input-number-input {
      text-align: center;
      height: 30px;
      padding-inline: 0;
      font-size: 12px;
    }
  }
`;

export const FontSizeAction = memo(({ editor, throttled = 100 }: FontSizeActionProps) => {
  const inputRef = useRef<FontSizeInputHandlerRef>(null);

  const bubbleMenuContainer = useTextEditorStore(state => state.bubbleMenuContainer);

  const { numberFontSize } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => {
      const fontSize = editorInstance.getAttributes('textStyle').fontSize || DEFAULT_TEXT_STYLE.fontSize;
      return {
        numberFontSize: fontSize.replace('px', ''),
      };
    },
  });

  const [currentFontSize, setCurrentFontSize] = useState<number>(numberFontSize);

  const updateEditorFontSize = useThrottledCallback((v: number) => {
    if (v === numberFontSize) return;

    editor.chain().focus().setFontSize(`${v}px`).run();
  }, throttled);

  const handleFontSizeChange = useCallback(
    (value: number) => {
      setCurrentFontSize(+value);
      updateEditorFontSize(+value);
    },
    [updateEditorFontSize],
  );

  const dropdownProps = useMemo(
    () => ({
      getPopupContainer: () => bubbleMenuContainer,
    }),
    [bubbleMenuContainer],
  );

  return (
    <StyledFontSizeInput
      ref={inputRef}
      placeholder="Font Size"
      value={currentFontSize}
      onChange={handleFontSizeChange}
      dropdownProps={dropdownProps}
    />
  );
});
