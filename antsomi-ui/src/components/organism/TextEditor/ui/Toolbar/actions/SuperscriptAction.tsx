import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface SuperscriptActionProps {
  editor: Editor;
}

const SuperscriptIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.8318 6.5625H18.4953V7.75H22V8.9375H17.3271V6.5625C17.3271 5.90937 17.8528 5.375 18.4953 5.375H20.8318V4.1875H17.3271V3H20.8318C21.4743 3 22 3.53437 22 4.1875V5.375C22 6.02813 21.4743 6.5625 20.8318 6.5625ZM2 22H5.10748L9.07944 15.5637H9.21963L13.1916 22H16.2991L10.8668 13.3669L15.9369 5.375H12.8061L9.21963 11.3006H9.07944L5.46963 5.375H2.36215L7.40888 13.3669L2 22Z"
      fill="currentColor"
    />
  </svg>
);

export const SuperscriptAction = ({ editor }: SuperscriptActionProps) => {
  const { isSuperscript, isSubscript, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isSuperscript: editorInstance.isActive('superscript'),
      isSubscript: editorInstance.isActive('subscript'),
      disabled: !editorInstance.can().chain().focus().toggleSuperscript().run(),
    }),
  });

  return (
    <TextEditorButton
      onClick={() => {
        if (isSubscript) {
          editor.chain().focus().toggleSubscript().toggleSuperscript().run();
        } else {
          editor.chain().focus().toggleSuperscript().run();
        }
      }}
      disabled={disabled}
      isActive={isSuperscript}
      icon={<SuperscriptIcon size={18} />}
      tooltipProps={{
        title: `${TOOLTIPS.SUPERSCRIPT.TITLE} (${TOOLTIPS.SUBSCRIPT.SHORTCUT})`,
      }}
    />
  );
};
