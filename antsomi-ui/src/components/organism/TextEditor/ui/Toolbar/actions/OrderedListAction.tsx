import { Editor } from '@tiptap/core';
import { ORDERED_LIST_STYLE_TYPE, TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { SplitButtonDropdown } from '../../SplitButtonDropdown';
import { CustomOrderedList } from '../../../extensions/OrderedList';
import { OrderedListStyleType, isOrderedListStyleType } from '../../../types';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface OrderedListActionProps {
  editor: Editor;
}

const ListOrderedIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M2 16.7857H4.10526V17.3214H3.05263V18.3929H4.10526V18.9286H2V20H5.15789V15.7143H2V16.7857ZM3.05263 9.28571H4.10526V5H2V6.07143H3.05263V9.28571ZM2 11.4286H3.89474L2 13.6786V14.6429H5.15789V13.5714H3.26316L5.15789 11.3214V10.3571H2V11.4286ZM7.26316 6.07143V8.21429H22V6.07143H7.26316ZM7.26316 18.9286H22V16.7857H7.26316V18.9286ZM7.26316 13.5714H22V11.4286H7.26316V13.5714Z"
      fill="currentColor"
    />
  </svg>
);

const OPTIONS: { label: string; value: OrderedListStyleType }[] = [
  {
    label: '1, 2, 3, ...',
    value: ORDERED_LIST_STYLE_TYPE.decimal,
  },
  {
    label: 'a, b, c, ...',
    value: ORDERED_LIST_STYLE_TYPE.lowerAlpha,
  },
  {
    label: 'A, B, C, ...',
    value: ORDERED_LIST_STYLE_TYPE.upperAlpha,
  },
  {
    label: 'i, ii, iii, ...',
    value: ORDERED_LIST_STYLE_TYPE.lowerRoman,
  },
  {
    label: 'I, II, III, ...',
    value: ORDERED_LIST_STYLE_TYPE.upperRoman,
  },
  {
    label: 'α, β, γ, ...',
    value: ORDERED_LIST_STYLE_TYPE.lowerGreek,
  },
  {
    label: '01, 02, 03, ...',
    value: ORDERED_LIST_STYLE_TYPE.decimalLeadingZero,
  },
];

export const OrderedListAction = ({ editor }: OrderedListActionProps) => {
  const { value, disabled, isSelected } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().toggleCustomOrderedList().run(),
      value: editorInstance.getAttributes(CustomOrderedList.name).listStyleType,
      isSelected: editorInstance.isActive(CustomOrderedList.name),
    }),
  });

  return (
    <SplitButtonDropdown
      disabled={disabled}
      value={value}
      options={OPTIONS}
      onClickOption={v => {
        if (!isOrderedListStyleType(v)) return;

        if (isSelected) {
          editor.chain().focus().setOrderedListStyleType(v).run();
          return;
        }

        editor
          .chain()
          .focus()
          .toggleCustomOrderedList({
            listStyleType: v,
          })
          .run();
      }}
      onClick={() => {
        editor.chain().focus().toggleCustomOrderedList().run();
      }}
      tooltipProps={{
        title: `${TOOLTIPS.ORDERED_LIST.TITLE} (${TOOLTIPS.ORDERED_LIST.SHORTCUT})`,
      }}
    >
      <ListOrderedIcon size={18} />
    </SplitButtonDropdown>
  );
};
