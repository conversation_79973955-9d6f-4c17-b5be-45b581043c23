import { Editor } from '@tiptap/core';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface SubscriptActionProps {
  editor: Editor;
}

const SubscriptIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.8318 19.625H18.4953V20.8125H22V22H17.3271V19.625C17.3271 18.9719 17.8528 18.4375 18.4953 18.4375H20.8318V17.25H17.3271V16.0625H20.8318C21.4743 16.0625 22 16.5969 22 17.25V18.4375C22 19.0906 21.4743 19.625 20.8318 19.625ZM2 19.625H5.10748L9.07944 13.1887H9.21963L13.1916 19.625H16.2991L10.8668 10.9919L15.9369 3H12.8061L9.21963 8.92562H9.07944L5.46963 3H2.36215L7.40888 10.9919L2 19.625Z"
      fill="currentColor"
    />
  </svg>
);

export const SubscriptAction = ({ editor }: SubscriptActionProps) => {
  const { isSubscript, isSuperscript, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isSubscript: editorInstance.isActive('subscript'),
      isSuperscript: editorInstance.isActive('superscript'),
      disabled: !editorInstance.can().chain().focus().toggleSubscript().run(),
    }),
  });

  return (
    <TextEditorButton
      onClick={() => {
        if (isSuperscript) {
          editor.chain().focus().toggleSuperscript().toggleSubscript().run();
        } else {
          editor.chain().focus().toggleSubscript().run();
        }
      }}
      disabled={disabled}
      isActive={isSubscript}
      icon={<SubscriptIcon size={18} />}
      tooltipProps={{
        title: `${TOOLTIPS.SUBSCRIPT.TITLE} (${TOOLTIPS.SUBSCRIPT.SHORTCUT})`,
      }}
    />
  );
};
