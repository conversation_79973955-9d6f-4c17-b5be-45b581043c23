import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface OutdentActionProps {
  editor: Editor;
}

const OutdentIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M10.8889 17.5556H22V15.3333H10.8889V17.5556ZM2 12L6.44444 16.4444V7.55556L2 12ZM2 22H22V19.7778H2V22ZM2 2V4.22222H22V2H2ZM10.8889 8.66667H22V6.44444H10.8889V8.66667ZM10.8889 13.1111H22V10.8889H10.8889V13.1111Z"
      fill="currentColor"
    />
  </svg>
);

export const OutdentAction = ({ editor }: OutdentActionProps) => {
  const { disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().outdent().run(),
    }),
  });

  return (
    <TextEditorButton
      disabled={disabled}
      icon={<OutdentIcon size={18} />}
      onClick={() => editor.chain().focus().outdent().run()}
      tooltipProps={{
        title: `${TOOLTIPS.OUTDENT.TITLE} (${TOOLTIPS.OUTDENT.SHORTCUT})`,
      }}
    />
  );
};
