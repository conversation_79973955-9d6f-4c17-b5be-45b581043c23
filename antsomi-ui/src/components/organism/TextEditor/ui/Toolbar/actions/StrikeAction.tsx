import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface StrikeActionProps {
  editor: Editor;
}

const StrikethroughIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M9.77778 21H14.2222V17.6H9.77778V21ZM4.22222 4V7.4H9.77778V10.8H14.2222V7.4H19.7778V4H4.22222ZM2 15.3333H22V13.0667H2V15.3333Z"
      fill="currentColor"
    />
  </svg>
);

export const StrikeAction = ({ editor }: StrikeActionProps) => {
  const { isStrike, disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      isStrike: editorInstance.isActive('strike'),
      disabled: !editorInstance.can().chain().focus().toggleStrike().run(),
    }),
  });

  return (
    <TextEditorButton
      icon={<StrikethroughIcon size={18} />}
      disabled={disabled}
      isActive={isStrike}
      onClick={() => editor.chain().focus().toggleStrike().run()}
      tooltipProps={{
        title: `${TOOLTIPS.STRIKE.TITLE} (${TOOLTIPS.STRIKE.SHORTCUT})`,
      }}
    />
  );
};
