import { Editor } from '@tiptap/core';
import { TextEditorButton } from '../../Button';
import { TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

export interface IndentActionProps {
  editor: Editor;
}

const IndentIcon = ({ size }: { size: number }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M2 22H22V19.7778H2V22ZM2 7.55556V16.4444L6.44444 12L2 7.55556ZM10.8889 17.5556H22V15.3333H10.8889V17.5556ZM2 2V4.22222H22V2H2ZM10.8889 8.66667H22V6.44444H10.8889V8.66667ZM10.8889 13.1111H22V10.8889H10.8889V13.1111Z"
      fill="currentColor"
    />
  </svg>
);

export const IndentAction = ({ editor }: IndentActionProps) => {
  const { disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().indent().run(),
    }),
  });

  return (
    <TextEditorButton
      disabled={disabled}
      icon={<IndentIcon size={18} />}
      onClick={() => editor.chain().focus().indent().run()}
      tooltipProps={{
        title: `${TOOLTIPS.INDENT.TITLE} (${TOOLTIPS.INDENT.SHORTCUT})`,
      }}
    />
  );
};
