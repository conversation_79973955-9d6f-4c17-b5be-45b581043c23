// Libraries
import { Editor } from '@tiptap/core';

// Hooks
import { useLinkHandler } from '../../../hooks/useLinkHandler';
import { LinkOffIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { TextEditorButton } from '../../Button';
import { useEditorState } from '@tiptap/react';

interface UnsetLinkActionProps {
  editor: Editor;
}

export const UnsetLinkAction = (props: UnsetLinkActionProps) => {
  const { editor } = props;

  const { unsetLink } = useLinkHandler({ editor });

  const { disabled } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      disabled: !editorInstance.can().chain().focus().unsetLink().run(),
    }),
  });

  return (
    <TextEditorButton
      tooltipProps={{
        title: 'Remove link',
      }}
      disabled={disabled}
      icon={<LinkOffIcon size={18} />}
      onClick={() => unsetLink()}
    />
  );
};
