import {
  Color as TiptapColorExtension,
  ColorOptions as TiptapColorOptions,
} from '@tiptap/extension-text-style';
import tinycolor from 'tinycolor2';

interface ColorOptions extends TiptapColorOptions {
  defaultColor: string;
}

export const Color = TiptapColorExtension.extend<ColorOptions>({
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          color: {
            default: null,
            parseHTML: element => element.style.color?.replace(/['"]+/g, ''),
            renderHTML: attrs => {
              if (!attrs.color || tinycolor.equals(attrs.color, this.options.defaultColor)) {
                return {};
              }

              return {
                style: `color: ${attrs.color}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      ...this.parent?.(),

      unsetColor:
        () =>
        ({ chain }) =>
          chain()
            .setMark('textStyle', { color: this.options.defaultColor || null })
            .removeEmptyTextStyle()
            .run(),
    };
  },
});
