import {
  FontFamilyOptions as TiptapFontFamilyOptions,
  FontFamily as TiptapFontFamilyExtension,
} from '@tiptap/extension-text-style';

interface FontFamilyOptions extends TiptapFontFamilyOptions {
  defaultFontFamily: string;
}

export const FontFamily = TiptapFontFamilyExtension.extend<FontFamilyOptions>({
  addCommands() {
    return {
      ...this.parent?.(),

      unsetFontFamily:
        () =>
        ({ chain }) =>
          chain()
            .setMark('textStyle', {
              fontFamily: this.options.defaultFontFamily || null,
            })
            .removeEmptyTextStyle()
            .run(),
    };
  },

  addGlobalAttributes() {
    return [
      ...(this.parent?.() || []),

      {
        types: this.options.types,
        attributes: {
          fontFamily: {
            default: this.options.defaultFontFamily,

            renderHTML: attrs => {
              if (!attrs.fontFamily || attrs.fontFamily === this.options.defaultFontFamily) {
                return {};
              }

              return {
                style: `font-family: ${attrs.fontFamily}`,
              };
            },

            parseHTML: element => element.style.fontFamily || this.options.defaultFontFamily,
          },
        },
      },
    ];
  },
});
