import TiptapLinkExtension, { LinkOptions as TiptapLinkOptions } from '@tiptap/extension-link';
import { LINK_TEXT_COLOR } from '../constants';

export type LinkAttrs = { id: string; href: string } & Partial<{
  content: string;
  target: string | null;
  rel: string | null;
  class: string | null;
  title: string | null;
  dynamic: boolean | null;
  tag: string | null;
  elementName: string | null;
}>;

export type LinkOptions = TiptapLinkOptions & {};

export const Link = TiptapLinkExtension.extend<LinkOptions>({
  addAttributes() {
    return {
      ...this.parent?.(),
      id: {
        default: null,
        parseHTML: el => el.getAttribute('data-link-id'),
        renderHTML: attrs => ({
          'data-link-id': attrs.id,
        }),
      },
      title: {
        default: null,
        parseHTML: el => el.title,
      },
      dynamic: {
        default: null,
        parseHTML: el => el.getAttribute('data-dynamic') === 'true',
        renderHTML: attrs => (attrs.dynamic ? { 'data-dynamic': attrs.dynamic } : null),
      },
      tag: {
        default: null,
        parseHTML: el => el.getAttribute('data-tag'),
        renderHTML: attrs => (attrs.tag ? { 'data-tag': attrs.tag } : null),
      },
      elementName: {
        default: null,
        parseHTML: el => el.getAttribute('data-el-name'),
        renderHTML: attrs => (attrs.elementName ? { 'data-el-name': attrs.elementName } : null),
      },
    };
  },

  addKeyboardShortcuts() {
    return {
      ...this.parent?.(),

      Enter: ({ editor }) => {
        editor.chain().unsetUnderline().run();

        const currentColor = editor.getAttributes('textStyle').color;

        if (currentColor === LINK_TEXT_COLOR) {
          editor.chain().unsetColor().run();
        }

        return false;
      },
    };
  },
});
