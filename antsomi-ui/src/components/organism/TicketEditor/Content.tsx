/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/no-unescaped-entities */
import React, { useMemo, useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import dayjs from 'dayjs';

import DropdownComponent from './components/DropdownComponent';
import MessageComponent from './components/MessageComponent';
import {
  Spin,
  Tooltip,
  Icon,
  Button,
  EditableName,
  SelectAccount,
  QuillEditor,
} from '@antscorp/antsomi-ui/es/components';
import Service from './Service';
import { MESSAGE_TYPE, TICKET_CUSTOM_MESSAGE_KEY } from './constant';

import {
  WrapperContent,
  WrapperContentInput,
  WrapperEditor,
  WrapperIconEditor,
  WrapperInputFile,
  WrapperLable,
  WrapperLeftContent,
  WrapperLinkItemFiles,
  WrapperMessageContent,
  WrapperRightContent,
  WrapperTextEdit,
  WrapperTextInput,
  DrawerHeader,
} from './styled';
import {
  formatAccountId,
  formatDatarender,
  formatParams,
  handleValidateContent,
  postCustomEvent,
} from './util';

type TFileZendesk = {
  token: string;
  url: string;
  size: number;
  file_name: string;
};

const initValueInput = {
  originTitle: 'Create new ticket',
  title: '',
  feature: {},
  ticketType: {},
  priority: {},
  category: {},
  ownerId: [],
  followers: [],
  message: '',
  files: [],
  fileZendesk: [] as TFileZendesk[],
  isChanged: false,
  referenceUrl: '',
};

const Content = ({
  portalId,
  token,
  action,
  ticketId,
  listUsers,
  domainTicket,
  domainUpload,
  config,
  ticketDetails,
  listComment,
  userId,
  fetchgetTicketComments,
  isLoadingComment,
  handleCloseDrawer,
  setIsSuccessCreated,
  isLoadingDetails,
  isLoadingDataSouce,
  accountManage,
  timeZone,
  isLoadingSelectedAccount,
  accountEdit,
  initOwnerId,
  isAllAccount,
  openDrawer,
  dataSelectInput,
  setDataSelectInput,
}) => {
  const [isOpenToast, setIsOpenToast] = useState({
    isOpen: false,
    status: 'error',
    messages: '',
  });
  const [textValue, setTextValue] = useState('');
  const [isEmptyField, setIsEmptyField] = useState(false);
  const [dataSelects, setDataSelect] = useState(dataSelectInput);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUpload, setIsLoadingUpload] = useState(false);
  const [isLoadingFollower, setIsLoadingFollower] = useState(false);
  const [valueInput, setValueInput] = useState({
    ...initValueInput,
    referenceUrl: window.location.href,
  });
  const [fileInputKey, setFileInputKey] = useState(0);

  const [errFile, setErrFile] = useState({
    isError: false,
    message: '',
  });
  const isUpdate = action === 'edit' && ticketId;
  const valueInputRef = useRef(valueInput);
  valueInputRef.current = valueInput;

  // const isRulesEdit = useMemo(() => {
  //   const { title, feature, ticketType, priority, category, ownerId, followers, message, files, referenceUrl } = valueInput
  //   if (
  //     !title?.length && referenceUrl === window.location.href
  //     && Object.keys(feature)?.length <= 2
  //     && Object.keys(ticketType)?.length <= 2 && (!Object.keys(priority)?.length || Object.values(priority)[0] === '-')
  //     && Object.keys(category)?.length <= 2 && ((!ownerId?.length && accountEdit === 4 && isAllAccount) || (ownerId[0]?.userId === Number(initOwnerId || userId)))
  //     && followers?.length === accountManage?.length
  //     && !message?.length && !files.length && !isUpdate
  //   ) {
  //     return false
  //   }else if(isUpdate && compareArrays(valueInput.followers, ticketDetails.followers)
  //     && !valueInput?.message?.length && !valueInput?.files?.length) {
  //     return false
  //   }
  //   return true
  // }, [valueInput])

  const updateValueInput = value => {
    setValueInput(prev => ({
      ...prev,
      ...value,
      isChanged: true,
    }));
  };

  const handleOnchangeInput = e => {
    const { name, value } = e.target;

    updateValueInput({
      [name]: value,
    });
  };

  const fetchCreateTicket = params => {
    setIsLoading(true);
    Service.tickets.callApi
      .createTicket({ ...params, networkId: Number(portalId) }, domainTicket, token, config, userId)
      .then(res => {
        setIsLoading(false);
        if (res.code === 200) {
          setIsSuccessCreated(true);
          postCustomEvent(TICKET_CUSTOM_MESSAGE_KEY, {
            type: MESSAGE_TYPE.TICKET_CREATE_STATUS,
            value: true,
          });
          // showMessage();
          handleCloseDrawer();
        } else {
          setIsSuccessCreated(false);
          postCustomEvent(TICKET_CUSTOM_MESSAGE_KEY, {
            type: MESSAGE_TYPE.TICKET_CREATE_STATUS,
            value: false,
          });
          // showMessage('error', 'Save failed!');
        }
      })
      .catch(err => {
        // console.log('err ===>', err)
        setIsLoading(false);
      });
  };

  const handleSubmit = () => {
    const { title, ownerId, message } = valueInputRef.current;
    if (!title || !ownerId.length || !message) {
      setIsEmptyField(true);
    } else {
      if (!errFile.isError) {
        const params = formatParams(valueInputRef.current);

        const submitterEmail = listUsers?.find(user => user.userId === params.ownerId)?.email;

        fetchCreateTicket({
          ...params,
          submitterId: Number(userId),
          submitterEmail: submitterEmail,
        });
      }
      // handleSendData(valueInput)
    }
  };

  const handleOnchangeTitle = title => {
    // const { value: title } = e.target;
    updateValueInput({
      title,
    });
  };

  useEffect(() => {
    if (!openDrawer) {
      setValueInput(initValueInput);
    }
  }, [openDrawer]);

  useEffect(() => {
    if (!isUpdate) {
      const newFollowers: any[] = [];
      let newOwnerIds = [];
      if (accountManage?.length > 0 && listUsers) {
        accountManage?.forEach(acc => {
          listUsers.forEach(user => {
            if (acc === user.userId) {
              newFollowers.push(user);
            }
          });
        });
      }
      if (
        (!isAllAccount || accountEdit !== 4) &&
        listUsers?.length > 0 &&
        (initOwnerId || userId)
      ) {
        newOwnerIds = listUsers?.filter(user => user.userId === Number(initOwnerId || userId));
      }
      updateValueInput({ ownerId: newOwnerIds, followers: newFollowers });
    }
  }, [isUpdate, accountManage, listUsers, accountEdit]);

  const browserTitle = useMemo(() => {
    if (valueInput.title && valueInput.title.trim()) {
      return valueInput.title;
    }

    return valueInput.originTitle;
  }, [valueInput.title, valueInput.originTitle]);

  const getCustomFields = () => {
    if (dataSelects && dataSelects.length) return;

    Service.tickets.callApi
      .getCustomFields({}, domainTicket, token, config, userId, 'get-custom-fields')
      .then(res => {
        if (res.code === 200) {
          setDataSelect(res?.data?.fields);
          setDataSelectInput(res?.data?.fields);
        }
      })
      .catch(err => {
        // console.log('err ===>', err)
      });
  };

  const fetchUpdateComment = params => {
    Service.tickets.callApi
      .updateComment(params, domainTicket, token, config, userId, 'create-ticket-comment', ticketId)
      .then(res => {
        if (res.code === 200) {
          fetchgetTicketComments(ticketId);
          updateValueInput({ message: '', files: [] });
          setTextValue('');
          // handleCloseDrawer();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const fetchUpdateFollwers = params => {
    setIsLoadingFollower(true);
    Service.tickets.callApi
      .updateFollowers(params, domainTicket, token, config, userId, ticketId)
      .then(res => {
        setIsLoadingFollower(false);
        if (res.code === 200) {
          // fetchgetTicketComments(ticketId)
          // updateValueInput({message: ''})
          // setTextValue('')
        }
      })
      .catch(err => {
        // console.log('err ===>', err)
        setIsLoadingFollower(false);
      });
  };
  useEffect(() => {
    if (
      Object.keys(ticketDetails)?.length &&
      isUpdate &&
      listUsers?.length &&
      dataSelects?.length
    ) {
      const {
        category,
        feature,
        followers,
        ownerId,
        priority,
        ticketType,
        title,
        referenceUrl,
        ...rests
      } = formatDatarender(ticketDetails, dataSelects, listUsers);
      updateValueInput({
        category,
        feature,
        followers,
        ownerId,
        priority,
        ticketType,
        title,
        referenceUrl,
        originTitle: title,
      });
    }
  }, [ticketDetails, dataSelects, listUsers]);

  useEffect(() => {
    setIsLoading(true);
    getCustomFields();

    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    if (!isUpdate && openDrawer) {
      updateValueInput({
        title: `Untitled Ticket#${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
      });
    }
  }, [isUpdate, openDrawer]);

  const handleEditorChange = content => {
    setTextValue(content);
    updateValueInput({ message: content });
    setIsLoadingUpload(content.includes('<img src="data:image/png;base64'));
  };

  const handleUpdateFollowers = arrFolowers => {
    const params = {
      followers: [],
    };
    if (arrFolowers?.length) {
      params.followers = arrFolowers;
    }
    fetchUpdateFollwers(params);
  };

  const handleUpdateComment = () => {
    setIsLoading(true);
    const params: any = {
      followers: [],
      submitterId: null,
      ownerId: null,
      submitterEmail: '',
      properties: {},
      message: '',
      attachmentsZendesk: [],
    };

    if (valueInput?.followers?.length) {
      params.followers = formatAccountId(valueInput?.followers);
    }
    if (valueInput?.files?.length > 0) {
      params.properties = {
        attachments: valueInput?.files,
      };
    }
    params.submitterId = userId;
    params.submitterEmail = listUsers?.find(user => +user.userId === +userId)?.email;
    params.ownerId = Number((valueInput?.ownerId[0] as any)?.userId);
    params.message = valueInput?.message;
    params.attachmentsZendesk = valueInput.fileZendesk.map(file => file.token);

    fetchUpdateComment(params);
    handleUpdateFollowers(valueInput?.followers?.map((fol: any) => fol.userId));
  };

  const handleUploadFile = async file => {
    setIsLoading(true);
    const uploadService = Service.tickets.callApi.uploadFile({
      domain: domainUpload,
      token,
      userId,
    });

    const uploadZendeskService = async listFiles => {
      const formData = new FormData();

      formData.append('file', listFiles);
      const params = {
        data: formData,
      };

      const responseZendesk = await Service.tickets.callApi.upload(
        params,
        domainTicket,
        token,
        config,
        userId,
        'ticket',
      );

      if (responseZendesk.code === 200) {
        updateValueInput({
          fileZendesk: [...valueInput?.fileZendesk, responseZendesk?.data],
        });
      }
    };

    try {
      const response = await uploadService!({ files: [file] });

      await uploadZendeskService(file);

      if (response.code === 200) {
        updateValueInput({
          files: [...valueInput?.files, response?.data[0]],
        });
      }

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timeOut = setTimeout(() => {
      if (isOpenToast.isOpen) {
        setIsOpenToast({ ...isOpenToast, isOpen: false });
      }
    }, 1000);
    return () => {
      clearTimeout(timeOut);
    };
  }, [isOpenToast]);

  const handleOnchangeFile = e => {
    const limitSize = 50 * 1024 * 1024;
    const sizeFile = e.target?.files[0]?.size;
    if (sizeFile >= limitSize) {
      setErrFile({
        isError: true,
        message: '*Maximum upload file size: 50MB',
      });
    } else {
      setErrFile({
        isError: false,
        message: '',
      });
      handleUploadFile(e.target.files[0]);
    }

    setFileInputKey(fileInputKey + 1);
  };

  const handleRemoveFile = (url: string, fileName: string) => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter((list: any) => list?.url !== url);
    updateValueInput({ files: newListFile });
  };

  const handleRemoveFileV2 = (index: number) => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter((_, fileIndex) => fileIndex !== index);

    const newFileZendesk = valueInput.fileZendesk.filter((_, fileIndex) => fileIndex !== index);

    updateValueInput({
      files: newListFile,
      fileZendesk: newFileZendesk,
    });

    setFileInputKey(prev => prev + 1);
  };

  return (
    <div style={{ height: '100%' }}>
      <Helmet>
        <meta charSet="utf-8" />
        <title>{browserTitle}</title>
      </Helmet>
      <Spin
        style={{ height: '100vh' }}
        spinning={
          isLoading ||
          isLoadingDetails ||
          isLoadingDataSouce ||
          isLoadingFollower ||
          isLoadingSelectedAccount
        }
      >
        {/* <WrapperHeader>
        {isOpenToast.isOpen && (
          <WrapperSnackbar
            open={isOpenToast.isOpen}
            autoHideDuration={1000}
            onClose={handleCloseSnackbar}
            background={`${isOpenToast.status === 'error' ? '#f43232' : '#32d932'}`}
            style={{ background: `${isOpenToast.status === 'error' ? '#f43232' : '#32d932'}` }}
            // anchorOrigin="top"
            message={isOpenToast.messages}
            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            // severity="error"
            // action={action}
          />
        )}
        <div style={{ color: '#000', fontSize: '16px' }}>{title}</div>
      </WrapperHeader> */}
        <DrawerHeader>
          <EditableName
            value={valueInput.title}
            onChange={handleOnchangeTitle}
            readonly={isUpdate}
            error={
              isEmptyField && (!valueInput.title || !valueInput.title.trim())
                ? "Title can't be empty"
                : ''
            }
          />
          <div id="header-journey-right" className="right-content">
            {!isUpdate && (
              <Button type="primary" onClick={handleSubmit}>
                Save
              </Button>
            )}
          </div>
        </DrawerHeader>
        <WrapperContent style={{ height: '100%' }}>
          <WrapperLeftContent>
            {/* <WrapperContentInput>
            <WrapperLable htmlFor="title">
              Title <span style={{ color: '#ff0000' }}>*</span>
            </WrapperLable>
            <div>
              {isUpdate ? (
                valueInput.title ? (
                  <Tooltip title={valueInput.title} placement="top">
                    <WrapperTextEdit>{valueInput.title}</WrapperTextEdit>
                  </Tooltip>
                ) : (
                  <WrapperTextEdit>{valueInput.title}</WrapperTextEdit>
                )
              ) : (
                <>
                  <WrapperTextInput
                    placeholder="Enter ticket title"
                    id="title"
                    value={valueInput.title}
                    onChange={handleOnchangeInput}
                    name="title"
                    maxLength={150}
                    isEmptyField
                  />
                  {isEmptyField && !valueInput.title && (
                    <div style={{ color: 'red', fontSize: '12px' }}>*This field can't be empty</div>
                  )}
                </>
              )}
            </div>
          </WrapperContentInput> */}
            <WrapperContentInput style={{ alignItems: 'flex-start' }}>
              <WrapperLable>
                Owner<span style={{ color: '#ff0000' }}>*</span>
              </WrapperLable>
              <div>
                <SelectAccount
                  type="default"
                  initData={(valueInput?.ownerId || []).map((item: any) => item.userId)}
                  nameKey="userName"
                  userIdKey="userId"
                  users={listUsers}
                  isViewMode={isUpdate || !isAllAccount || accountEdit !== 4}
                  onChange={(_, users) => updateValueInput({ ownerId: users })}
                  onlyOne
                />
                {/* <GroupAccountChip
                isAccount
                setValueInput={updateValueInput}
                valueInput={valueInput}
                type="ownerId"
                action={action}
                listUsers={listUsers}
                isDisabled={!isAllAccount || accountEdit !== 4}
              /> */}
                {isEmptyField && !valueInput?.ownerId?.length && (
                  <div className="error-message">*This field can't be empty</div>
                )}
              </div>
            </WrapperContentInput>
            <WrapperContentInput style={{ marginTop: '15px', alignItems: 'flex-start' }}>
              <WrapperLable>Follower(s)</WrapperLable>
              <SelectAccount
                type="default"
                initData={[
                  ...accountManage,
                  ...(valueInput?.followers || []).map((item: any) => item.userId),
                ]}
                nameKey="userName"
                userIdKey="userId"
                users={listUsers}
                disabledAccount={accountManage}
                onChange={(_, followers) => updateValueInput({ followers })}
              />
              {/* <GroupAccountChip
              setValueInput={updateValueInput}
              valueInput={valueInput}
              type="followers"
              listUsers={listUsers}
              idAccountManage={accountManage}
              isDisabled={false}
            /> */}
            </WrapperContentInput>
            {dataSelects?.map((data: any) => (
              // eslint-disable-next-line react/jsx-key
              <WrapperContentInput key={data.id} style={{ marginTop: '15px' }}>
                <WrapperLable>{data.title}</WrapperLable>
                <div style={{ flex: 1 }}>
                  {isUpdate ? (
                    <WrapperTextEdit>
                      {!valueInput[data?.value]?.name?.length
                        ? '--'
                        : valueInput[data?.value]?.name}
                    </WrapperTextEdit>
                  ) : (
                    <DropdownComponent
                      data={data.field_options}
                      setValueInput={updateValueInput}
                      valueInput={valueInput}
                      type={data.value}
                      isUpdate={isUpdate}
                      title={data.title}
                    />
                  )}
                </div>
              </WrapperContentInput>
            ))}
            {/* <Divider
            style={{
              backgroundColor: 'transparent',
              borderTop: '2px dashed #d4d4d4',
              margin: '15px 35px 15px 0',
              width: 'calc(100% - 35px)',
            }}
          /> */}
            {/* <WrapperContentInput flexStart>
            <WrapperLable style={{ lineHeight: '12px' }}>
              Notification Setup
            </WrapperLable>
            <Notification value={valueNotication} />
          </WrapperContentInput> */}
            <WrapperContentInput style={{ marginTop: '15px' }}>
              <WrapperLable htmlFor="referenceUrl">Reference URL</WrapperLable>
              {isUpdate ? (
                valueInput.referenceUrl ? (
                  <Tooltip title={valueInput.referenceUrl} placement="top">
                    <WrapperTextEdit color="#005fb8" href={valueInput.referenceUrl} target="_blank">
                      {valueInput.referenceUrl}
                    </WrapperTextEdit>
                  </Tooltip>
                ) : (
                  <WrapperTextEdit color="#005fb8" href={valueInput.referenceUrl} target="_blank">
                    {valueInput.referenceUrl}
                  </WrapperTextEdit>
                )
              ) : (
                <WrapperTextInput
                  placeholder="Reference URL"
                  id="referenceUrl"
                  // width="300px"
                  onChange={handleOnchangeInput}
                  name="referenceUrl"
                  value={valueInput.referenceUrl}
                />
              )}
            </WrapperContentInput>
          </WrapperLeftContent>
          <WrapperRightContent>
            <WrapperEditor>
              {/* <div
              style={{
                color: '#000',
                fontSize: '12px',
                marginBottom: '10px',
              }}
            >
              Message <span style={{ color: '#ff0000' }}>*</span>
            </div> */}
              <div>
                {/* <Editor
                  apiKey={apiKey}
                  // placeholder="Enter your comment..."
                  value={textValue}
                  init={{
                    height: 238,
                    width: '100%',
                    max_height: 800,
                    max_width: 800,
                    menubar: false,
                    plugins: [
                      'advlist autolink lists link tinydrive image emoticons charmap print preview anchor',
                      'searchreplace visualblocks code fullscreen',
                      'insertdatetime media paste code help wordcount ', //
                    ],
                    toolbar:
                      'formatselect | bold italic underline strikethrough code | image emoticons | \
                                  forecolor backcolor preview link |\
                                  alignleft aligncenter alignright alignjustify outdent indent |\
                                  numlist bullist checklist undo redo',
                    tinydrive_token_provider: `//${domain}/hub/thirdparty-services/v2.0/tinymce?portalId=${portalId}&token=${token}`,
                    skin: 'snow',
                    toolbar_mode: 'sliding',
                    content_css: false,
                    branding: false,
                    resize: false,
                    statusbar: false,
                    setup(editor) {
                      editor.on('init', e => {
                        editor.getBody().style.fontSize = '12px';
                      });
                    },
                    placeholder: 'Enter your comment...',
                    entity_encoding: 'raw',
                    paste_data_images: true,
                  }}
                  // disabled={!!props.disabled}
                  // outputFormat='text'
                  onEditorChange={handleEditorChange}
                /> */}
                <QuillEditor
                  value={textValue}
                  uploadService={Service.tickets.callApi.uploadImg({
                    domain: domainUpload,
                    token,
                    userId,
                  })}
                  onChange={handleEditorChange}
                  placeholder="Enter your comment..."
                  height={195}
                />
                <div>
                  {valueInput.files?.length > 0 && (
                    <WrapperLinkItemFiles>
                      {valueInput.files?.map((file: any, index) => (
                        <div className="file-item" key={file?.file_name}>
                          <div className="file-name-group">
                            <Icon className="file-icon" type="icon-ants-attachment" />
                            <Tooltip title={file?.file_name}>
                              <span className="file-name">{file?.file_name}</span>
                            </Tooltip>
                          </div>
                          {/* <Button type="text" onClick={() => handleRemoveFile(file?.token)}> */}
                          <Icon
                            onClick={() => {
                              handleRemoveFileV2(index);
                            }}
                            className="remove-btn"
                            type="icon-ants-remove-slim"
                          />
                          {/* </Button> */}
                        </div>
                      ))}
                    </WrapperLinkItemFiles>
                  )}
                  <WrapperIconEditor borderTop={!!valueInput.files?.length}>
                    <WrapperInputFile>
                      <label htmlFor={`fileImage-${fileInputKey}`} className="upload-wrapper-label">
                        <Icon type="icon-ants-attachment" className="upload-icon" />
                      </label>
                      <input
                        key={fileInputKey}
                        type="file"
                        style={{ position: 'absolute', top: 0, right: 0, display: 'none' }}
                        name={`fileImage-${fileInputKey}`}
                        id={`fileImage-${fileInputKey}`}
                        onChange={handleOnchangeFile}
                      />
                    </WrapperInputFile>
                    {isUpdate && (
                      <Button
                        type="primary"
                        disabled={!handleValidateContent(textValue) || isLoadingUpload}
                        className="reply-btn"
                        style={{
                          background: `${
                            !handleValidateContent(textValue) || isLoadingUpload
                              ? '#ccc'
                              : '#1f5fac'
                          }`,
                        }}
                        onClick={handleUpdateComment}
                        loading={isLoadingUpload}
                      >
                        Reply
                      </Button>
                    )}
                  </WrapperIconEditor>
                </div>
              </div>
              {errFile.isError ? (
                <div className="error-message">{errFile.message}</div>
              ) : (
                isEmptyField &&
                !valueInput?.message && (
                  <div className="error-message">*This field can't be empty</div>
                )
              )}
            </WrapperEditor>

            {isUpdate && (
              <Spin spinning={isLoadingComment}>
                <WrapperMessageContent>
                  {listComment?.map(comment => (
                    <MessageComponent
                      key={comment?.id}
                      toUser={comment?.toUser}
                      fromUser={comment?.fromUser}
                      followers={comment?.followers}
                      // mailFollower={comment?.mailFollower}
                      message={comment?.message}
                      date={comment?.createdDate}
                      attachments={comment?.attachments}
                      timeZone={timeZone}
                      submitterEmail={comment?.submitterEmail}
                    />
                  ))}
                </WrapperMessageContent>
              </Spin>
            )}
          </WrapperRightContent>
        </WrapperContent>
      </Spin>
      {/* {!isUpdate && (
        <WrapperFooter>
          <Button
            style={{
              height: '28px',
              background: '#1f5fac',
              color: '#fff',
              fontSize: '12px',
              borderRadius: '3px',
              fontWeight: 'bold',
              textTransform: 'capitalize',
            }}
            onClick={handleSubmit}
          >
            Send
          </Button>
          <Button
            style={{
              height: '28px',
              background: '#fff',
              color: '#005fb8',
              fontSize: '12px',
              borderRadius: '3px',
              fontWeight: 'bold',
              textTransform: 'capitalize',
              border: '1px solid #edeef7',
            }}
            onClick={() => handleClose(false, valueInput.isChanged)}
          >
            Cancel
          </Button>
        </WrapperFooter>
      )} */}
    </div>
  );
};

export default Content;
