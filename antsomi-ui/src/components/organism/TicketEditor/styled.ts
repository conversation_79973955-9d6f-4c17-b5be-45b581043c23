import styled from 'styled-components';
import { Button } from '@antscorp/antsomi-ui/es/components/atoms/Button';
import { Checkbox } from '@antscorp/antsomi-ui/es/components/atoms/Checkbox';
import { ModalV2 } from '@antscorp/antsomi-ui/es/components/molecules/ModalV2';

export const WrapperHeader = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  align-items: center;
  border-bottom: 1px solid #e5e5e5;
  height: 50px;
`;

export const WrapperContent = styled.div`
  display: flex;
  gap: 20px;
  padding: 15px;
`;

export const WrapperLeftContent = styled.div`
  width: 38%;
  overflow: auto;
  // border-right: 1px solid #e5e5e5;
  overflow: auto;
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f2f2f2;
  }

  ::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
`;

export const WrapperRightContent = styled.div`
  flex: 1;
  overflow: auto;
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f2f2f2;
  }

  ::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
`;

export const WrapperTextEdit = styled.a`
  white-space: nowrap;
  font-size: 12px;
  color: ${props => (props.color ? props.color : '#000')};
  text-decoration: none;
  cursor: ${props => (props.color ? 'pointer' : 'default')};
  max-width: 300px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  &:hover {
    color: ${props => (props.color ? props.color : '#000')};
  }
`;

export const WrapperTextInput = styled.input`
  width: ${props => (props.width ? props.width : 'unset')};
  min-width: ${props => (props.width ? props.width : 'unset')};
  height: 30px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #b8cfe6;
  outline: none;
  flex: 1;
  &:hover {
    background-color: rgb(242, 249, 255);
  }
  font-size: 12px;
  color: #000;
`;
export const WrapperContentInput = styled.div<{
  flexStart?: boolean;
}>`
  display: flex;
  align-items: ${props => (props.flexStart ? 'flex-start' : 'center')};

  .error-message {
    font-size: 12px;
    color: red;
    margin-top: 5px;
  }
`;

export const WrapperLable = styled.label`
  width: 165px;
  min-width: 165px;
  color: #666;
  text-align: left;
  display: inline-block;
  font-size: 12px;
`;

export const WrapperDropdown = styled.div`
  position: relative;
`;

export const WrapperToggleDropdown = styled.div`
  &.wrapper-select-options {
    background-color: #fff !important;
    border-radius: 0;
    border-top: none;
    border-left: none;
    width: 300px;
    height: 30px;
    border-right: none;
    border-bottom: 1px solid rgb(184, 207, 230);
    outline: none;
    color: #000 !important;
    border-color: rgb(184, 207, 230) !important;
    display: flex;
    align-items: center;
    padding: 0 4px;
    justify-content: space-between;
    &:hover {
      color: #000 !important;
      background-color: rgb(242, 249, 255) !important;
      border-color: rgb(184, 207, 230) !important;
      outline: none !important;
      box-shadow: none !important;
    }
    &:focus {
      color: #000 !important;
      background-color: #fff !important;
      border-color: rgb(184, 207, 230) !important;
      outline: none !important;
      box-shadow: none !important;
    }
    &:active {
      color: #000 !important;
      background-color: #fff !important;
      border-color: rgb(184, 207, 230) !important;
      outline: none !important;
      box-shadow: none !important;
    }
  }
`;

export const WrapperDropdownMenu = styled.div`
  box-shadow:
    0px 5px 5px -3px rgb(0 0 0 / 20%),
    0px 8px 10px 1px rgb(0 0 0 / 14%),
    0px 3px 14px 2px rgb(0 0 0 / 12%);
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  position: absolute;
  z-index: 10;
  top: 30;
  right: 0;
  padding: 8px 0;
  border-radius: 4px;
  background: #fff;
  border: none !important;
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f2f2f2;
  }

  ::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
`;

export const WrapperDropdownItem = styled.div`
  color: #33475b !important;
  font-size: 14px;
  padding: 5px 12px;
  &:hover {
    color: #000 !important;
    background-color: rgb(242, 249, 255) !important;
    border-color: rgb(184, 207, 230) !important;
    outline: none !important;
    box-shadow: none !important;
  }
  &:focus {
    color: #000 !important;
    background-color: #fff !important;
    border-color: rgb(184, 207, 230) !important;
    outline: none !important;
    box-shadow: none !important;
  }
  &:active {
    color: #000 !important;
    background-color: #fff !important;
    border-color: rgb(184, 207, 230) !important;
    outline: none !important;
    box-shadow: none !important;
  }
`;
export const WrapperLabelDropdown = styled.div`
  color: #000;
  font-size: 12px;
`;

export const MaterialChip = styled.div`
  margin: 0;
  background-color: #cae5fe;
  color: #000;
  font-size: 12px;
  position: relative;
  border-radius: 12px;
  display: flex;
  padding: 10px;
  align-items: center;
  height: 24px;
  width: fit-content;
  .content-chip {
    margin: 0 5px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &:hover .delete-button {
    display: flex;
  }
  .delete-button {
    display: none;
    right: 5px;
    position: absolute;
    top: 4px;
    z-index: 10;
    border: 0;
    cursor: pointer;
    outline: none;
    background: #fff;
    color: #005eb8;
    font-size: 12px;
    border-radius: 50%;
    span {
      height: 16px;
      width: 16px;
      padding: 0px;
      color: #000;
      font-size: 12px;
    }
  }
`;

export const Wrapper = styled.div`
  overflow-x: hidden;
  align-items: center;
  cursor: pointer;
  display: flex;
  min-width: 0;
  .chip-container {
    margin: 0px 4px 0 0px;
    overflow: hidden;
    font-size: 0.813rem;
    font-family: Roboto-Bold;
    height: 24px;
  }
`;

export const DivLoading = styled.div`
  height: 30px;
  width: 20%;
  position: relative;
  .private-overlay {
    padding: 0 !important;
  }
`;

export const WrapperListAccount = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  width: 400px;
  min-width: 400px;
`;

export const Title = styled.div`
  color: #666;
  font-size: 12px;
  margin-bottom: 20px;
`;

export const TitleName = styled.p`
  color: #000;
  font-size: 12px;
  white-space: nowrap;
  margin-bottom: 10px;
`;

export const WrapperNotication = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const WrapperFooter = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  height: 58px;
  padding-left: 15px;
  border-top: 1px solid #e5e5e5;
`;

export const WrapperEditor = styled.div`
  // padding: 15px;
  .tox-tinymce {
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    border-bottom: none !important;
  }

  .error-message {
    font-size: 12px;
    color: red;
    margin-top: 5px;
  }

  .tox {
    .tox-tbtn--enabled {
      background: #deeffe;
    }
    .tox-tbtn {
      &:hover {
        background: #f2f9ff;
      }
      svg {
        fill: #595959;
      }
    }
  }
`;

export const WrapperListAccountSearch = styled.div`
  display: flex;
  flex-direction: column;
  max-height: 300px;
  overflow-y: auto;
  gap: 8px;
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f2f2f2;
  }

  ::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
`;

export const WrapperItemAccountSearch = styled.div`
  display: flex;
  cursor: pointer;
  align-items: center;
  gap: 3px;
  padding-left: 15px;
`;

export const WrapperFooterPopup = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-top: 1px solid #dbe9f7;
  padding: 15px;
  gap: 15px;
`;

export const WrapperIconEditor = styled.div<{
  borderTop?: boolean;
}>`
  // border-top: ${props => (props.borderTop ? '1px solid #D4D4D4' : 'none')};
  border: 1px solid #d4d4d4;
  border-top: none !important;
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: space-between;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  height: 50px;

  .reply-btn {
  }
`;

export const WrapperInputFile = styled.div`
  position: relative;
  width: 24px;
  height: 24px;

  .upload-wrapper-label {
    display: block;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .upload-icon {
    color: #005eb8;
    font-size: 20.5px;
    width: 24px;
    cursor: pointer;
  }
`;

export const WrapperLinkItemFiles = styled.div`
  border-right: 1px solid #d4d4d4;
  border-left: 1px solid #d4d4d4;

  &.no-border {
    border: none !important;
  }

  .image-item {
    img {
      max-width: 100%;
      object-fit: contains;
    }
  }

  .file-item {
    color: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 30px;
    height: 30px;
    padding: 0 14px 0 10px;

    &.rounded {
      border-radius: 10px;
      padding: 0 10px;
    }

    .file-name-group {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .file-icon {
      font-size: 20.5px;
      color: #595959;
    }
    .file-name {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .remove-btn {
      font-size: 12px;
      color: #595959;
      cursor: pointer;
      display: none;
    }
    .download-btn {
      font-size: 24px;
      color: #595959;
      cursor: pointer;
      display: none;
    }

    &:hover {
      background: #f2f9ff;
      .remove-btn {
        display: block;
      }
      .download-btn {
        display: block;
      }
    }
  }
`;

export const WrapperSnackbar = styled.div<{
  background?: string;
}>`
  .MuiPaper-root.MuiSnackbarContent-root.MuiPaper-elevation6 {
    background: ${props => props.background};
    min-width: unset;
    text-align: center;
    padding: 4px;
    .MuiSnackbarContent-message {
      width: 100%;
      text-align: center;
      padding: 0px;
    }
  }
`;
export const WrapperModalHeader = styled.div`
  font-size: 16px;
  color: #000;
  font-weight: bold;
  padding: 16px;
  border-bottom: 1px solid #dee2e6;
`;

export const WrapperAvatar = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 10px;

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: contains;
  }
  .user-info {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    color: #000;

    &__name {
      font-weight: 600;
      line-height: 14.06px;
    }
    &__to-user {
      color: #7f7f7f;
      display: flex;
      align-items: center;
      gap: 8px;
      line-height: 14.06px;
    }
    &__expand-icon {
      font-size: 7px;
      color: #7f7f7f;
      cursor: pointer;

      &.open {
        transform: rotate(180deg);
        position: relative;
        top: 3px;
      }
    }
    &__desc {
      line-height: 13px;
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    &__followers {
      display: flex;
      align-items: flex-start;
      gap: 10px;
    }
    .icon-wrapper {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(90deg);
      transform-origin: center;
    }
  }
`;

export const WrapperContentAvatar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;

  .comment-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
  }
  .comment-time {
    transform: translateX(-50%);
    width: fit-content;
    font-size: 12px;
    color: #7f7f7f;
    margin-right: 5px;
    white-space: nowrap;
  }
  .comment-content {
    font-size: 12px;
    color: #333;

    .gmail_chip {
      height: 42px !important;
      max-height: 42px !important;
    }
  }
`;

export const WrapperMessageContent = styled.div`
  // height: calc(100% - 354px);
  min-height: 150px;
  overflow: auto;
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background: #f2f2f2;
  }

  ::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
`;

export const WrapperLoading = styled.div<{
  width?: string;
  height?: string;
}>`
  width: ${props => (props.width ? props.width : 'calc(100% - 100px)')};
  height: ${props => (props.height ? props.height : '100%')};
  position: fixed;
  background: rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
`;
export const WrapperBtnAddAcc = styled(Button)`
  &.MuiButton-outlined {
    display: flex;
    align-items: center;
    border: 1px solid #005eb8;
    height: 24px;
    border-radius: 20px;
    padding: 10px;
  }
  &.MuiButton-root:hover {
    background: #fff;
  }
`;

export const WrapperModalImg = styled(ModalV2)`
  &.image-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    // height: 100vh;
    width: auto !important;
    .modal-content {
      border: none;
      background: transparent;
      height: 100%;
      width: 100%;
    }
    .antsomi-modal-close {
      top: 5px !important;
      right: 5px !important;
    }
    .antsomi-modal-footer {
      padding: 0 !important;
      border-top-width: 0 !important;
    }
    .modal-img-content {
      display: 'flex',
      align-items: 'flex-start',
      justify-content: 'center',
      width: '100%',
      height: 'calc(100% - 20px)',
      overflow: 'auto',
    }
  }
`;

export const WrapperModalHeaderImg = styled.div`
  height: 20px;
  width: calc(100% + 40px);
  display: flex;
  justify-content: flex-end;
`;
export const TicketButton = styled.button`
  width: 36px;
  height: 36px;
  border: 1px solid #e0e6eb;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #fff;
  cursor: pointer;

  &:focus {
    outline: none;
  }

  > .add-icon {
    position: absolute;
    bottom: -3px;
    right: -3px;
  }
`;

export const WrapperCheckBox = styled(Checkbox)`
  &.MuiCheckbox-colorPrimary.Mui-checked {
    color: #005eb8;
  }
`;

export const WrapperMessage = styled.div`
  &.container-content {
    z-index: 10000;
    bottom: 40px;
    left: 10px;
    position: fixed;
    width: 345px;
    height: 72px;
    animation: myfirst 0.2s ease-in-out;
    @keyframes myfirst {
      0% {
        left: -150px;
      }
      25% {
        left: -100px;
      }
      50% {
        left: -50px;
      }
      75% {
        left: 0px;
      }
      100% {
        left: 10px;
      }
    }
  }

  .content {
    background: #333;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    padding: 12px 24px;
  }

  .icon-check {
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .check-icon {
      font-size: 9px;
    }
  }

  .floating {
    color: '#6ac259';
    font-size: 14px;
    align-items: center;
    // justify-content: space-between;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 0.375em 0px !important;
    border-left: 0.35rem solid;
    border-radius: 0.35rem;
    background-color: white;
    padding: 0.75rem 1.5rem;
    position: relative;

    display: inline-flex;
    max-width: 100%;
    pointer-events: auto;
    text-align: left;
    height: 72px;
    width: 435px;
    .alert__title {
      font-weight: 700;
      /* text-shadow: 0 0 1px transparent; */
      margin-bottom: 0.5rem;
      margin-top: 0;
      display: block;
      line-height: normal;
      text-transform: none;
      font-size: 16px !important;
      color: inherit;
    }
    .secondary__title {
      color: grey;
    }
    .floating-alert-list__child-wrapper {
      left: 0;
      overflow: auto;
      position: absolute;
      top: 0;
      width: 100%;
    }
    .noti-content {
      min-width: 200px;
    }
  }
`;

export const TicketEditorWrapper = styled.div`
  .modal {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .wrapperBody {
    background: #fff;
    width: 500px;
    border-radius: 4px;
  }
  .modalBody {
    padding: 16px;
  }
  .modalFooter {
    padding: 12px;
    justify-content: flex-start;
    gap: 10px;
    display: flex;
  }
  .popoverBody {
    padding: 0;
  }
  .popoverItem {
    cursor: pointer;
    padding: 5px 10px;
    font-size: 13px !important;

    &:hover {
      background-color: #ddd;
    }
  }
`;

// export const DrawerHeader = styled.div`
//   background: #ffffff;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   width: 100%;

//   .left-content {
//     display: flex;
//     align-items: center;
//     gap: 15px;
//   }

//   .right-content {
//     display: flex;
//     align-items: center;
//     gap: 15px;

//     .header-info-drawer {
//       display: flex;
//       flex-direction: column;
//       gap: 5px;
//       padding: 4.5px 0;

//       > div {
//         display: flex;
//         align-items: center;
//         gap: 10px;
//         justify-content: end;
//       }

//       p {
//         margin: 0;
//       }
//     }
//   }
// `;

export const DrawerHeader = styled.div`
  position: sticky;
  top: 0px;
  height: 50px;
  padding: 0px 15px;
  box-sizing: border-box;
  background-color: #ffffff;
  flex-shrink: 0;
  z-index: 20;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 100px;
  width: 100%;
  max-width: 100%;

  .left-content {
    display: flex;
    align-items: center;
    gap: 15px;
    max-width: calc(100% - 150px);

    #header-journey-name {
      max-width: 100%;
    }
  }

  .right-content {
    display: flex;
    align-items: center;
    gap: 15px;

    .header-info-drawer {
      display: flex;
      flex-direction: column;
      gap: 5px;
      padding: 4.5px 0;

      > div {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: end;
      }

      p {
        margin: 0;
      }
    }
  }
`;
