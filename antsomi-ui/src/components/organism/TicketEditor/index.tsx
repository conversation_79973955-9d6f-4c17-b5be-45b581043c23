import React, { useEffect, useState } from 'react';
import Content from './Content';
import Service from './Service';
import TicketIcon from './components/icons/TicketIcon';
import AddTicketIcon from './components/icons/AddTicketIcon';
import {
  Button,
  Modal,
  Popover,
  Typography,
  DrawerDetail,
  message,
  Icon,
} from '@antscorp/antsomi-ui/es/components/';
import { NoticeType } from 'antd/es/message/interface';

import { WrapperModalHeader, TicketButton, TicketEditorWrapper } from './styled';
// import Message from './components/Message';

const defaultProps = {
  isOpen: false,
  handleCloseDrawer: () => null,
  portalId: '',
  token: '',
  domain: '',
  action: '',
  config: {},
  userId: '',
  ownerId: '',
  domainTicket: '',
  ticketId: '',
  domainAccount: '',
  useIcon: false,
  domainPermission: '',
  appCode: 'TICKET_MANAGEMENT',
  menuCode: 'TICKET',
  fullParent: false,
  useChat: false,
};

export const TicketEditor = props => {
  const {
    isOpen,
    portalId,
    token,
    domain,
    action,
    config: propsConfig,
    userId,
    ownerId,
    domainTicket,
    domainUpload,
    ticketId,
    domainAccount,
    useIcon,
    domainPermission,
    appCode,
    menuCode,
    fullParent,
    useChat,
  } = props;

  const [openDrawer, setOpenDrawer] = useState(false);
  const [isOpenPopover, setOpenPopover] = useState(false);
  const [openChat, setOpenChat] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [listUsers, setListUsers] = useState([]);
  const [ticketDetails, setTicketDetails] = useState({});
  const [listComment, setListComment] = useState([]);
  const [isLoadingComment, setIsLoadingComment] = useState(false);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [isLoadingSelectedAccount, setIsLoadingSelectedAccount] = useState(false);
  const [accountEdit, setAccountEdit] = useState(null);
  const [isLoadingDataSouce, setIsLoadingDataSouce] = useState(false);
  const [accountManage, setAccountManage] = useState([]);
  const [timeZone, setTimeZone] = useState('');
  // const [isSuccessCreated, setIsSuccessCreated] = useState(false);
  const [dataSelectInput, setDataSelectInput] = useState([]);
  const config = typeof propsConfig === 'string' ? JSON.parse(atob(propsConfig)) : propsConfig;
  const isAllAccount = !ownerId;

  const [messageApi, contextHolder] = message.useMessage();

  const showMessage = (type = 'success', content = 'Create ticket successfully!') => {
    messageApi.open({
      type: type as NoticeType,
      content,
    });
  };

  // const useStyles = makeStyles((theme) => ({
  //   modal: {
  //     display: 'flex',
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //   },
  //   wrapperBody: {
  //     background: '#fff',
  //     width: 500,
  //     borderRadius: 4
  //   },
  //   modalBody: {
  //     padding: 16
  //   },
  //   modalFooter: {
  //     padding: 12,
  //     justifyContent: 'flex-start',
  //     gap: 10,
  //     display: 'flex'
  //   }
  // }),
  // {
  //   classNamePrefix: 'UITicket_Editor',
  // });

  // const classes = useStyles();

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    props.handleCloseDrawer();
  };

  const fetchDataSouce = () => {
    setIsLoadingDataSouce(true);
    Service.lookupInfo.callApi
      .c_user_id({}, domain, token, config, userId)
      .then(res => {
        setIsLoadingDataSouce(false);
        if (res.code === 200) {
          setListUsers(res?.data);
          // if (res?.data?.entries[0].length > 0) {
        }
      })
      .catch(err => {
        setIsLoadingDataSouce(false);
        // console.log('err ===>', err)
      });
  };

  const fetchAccountPermission = () => {
    setIsLoadingSelectedAccount(true);
    const params = {
      appCode,
      menuCode,
      fullParent,
    };
    Service.permission.callApi
      .getList(params, domainPermission, token, config, userId, 'list-menu', ownerId)
      .then(res => {
        setIsLoadingSelectedAccount(false);
        if (res.code === 200) {
          setAccountEdit(res?.data[0]?.selected_edit);
        }
      })
      .catch(err => {
        setIsLoadingSelectedAccount(false);
      });
  };

  const fetchAccountManage = () => {
    // setIsLoadingDataSouce(true)
    Service.accounts.callApi
      .getAccountManage({}, domainAccount, token, config, userId, 'account')
      .then(res => {
        // setIsLoadingDataSouce(false)
        if (res.code === 200) {
          const listIds = res?.data?.infoNetwork?.accountManager?.map(item => item.user_id);
          setAccountManage(listIds);
          setTimeZone(res?.data?.infoNetwork?.timezone);
        }
      })
      .catch(err => {
        // setIsLoadingDataSouce(false)
        // console.log('err ===>', err)
      });
  };

  const fetchDetailsTicket = ticketId => {
    setIsLoadingDetails(true);
    Service.tickets.callApi
      .getDetailsTicket({}, domainTicket, token, config, userId, ticketId)
      .then(res => {
        setIsLoadingDetails(false);
        if (res.code === 200) {
          setTicketDetails(res?.data?.ticket);
        }
      })
      .catch(err => {
        setIsLoadingDetails(false);
        // console.log('err ===>', err)
      });
  };

  const fetchgetTicketComments = ticketId => {
    setIsLoadingComment(true);
    Service.tickets.callApi
      .getListComment({}, domainTicket, token, config, userId, 'get-ticket-comments', ticketId)
      .then(res => {
        if (res.code === 200) {
          setListComment(res?.data);
          // setListUsers(res?.data)
          // setDataSelect(res?.data)
          // if (res?.data?.entries[0].length > 0) {
        } else {
          setListComment([]);
        }
      })
      .finally(() => {
        setIsLoadingComment(false);
      });
  };

  const setIsSuccessCreated = isSuccess => {
    if (isSuccess) {
      showMessage();
    } else {
      showMessage('error', 'Create ticket failed!');
    }
  };

  useEffect(() => {
    if (ticketId && action !== 'create' && isOpen) {
      fetchDetailsTicket(ticketId);
      fetchgetTicketComments(ticketId);
    }
  }, [ticketId, isOpen]);

  useEffect(() => {
    fetchDataSouce();
    fetchAccountManage();
    fetchAccountPermission();
  }, []);

  const toggleModal = () => {
    setIsOpenModal(!isOpenModal);
  };

  useEffect(() => {
    setOpenDrawer(isOpen);
    // if (isOpen) {
    //   setIsSuccessCreated(false);
    // }
  }, [isOpen]);

  const handleClose = (isCancle, isRulesEdit) => {
    if (isCancle) {
      handleCloseDrawer();
    } else if (isRulesEdit) {
      setIsOpenModal(true);
    } else {
      handleCloseDrawer();
    }
    setListComment([]);
  };

  const handleConfirmModal = () => {
    setIsOpenModal(false);
    handleCloseDrawer();
  };

  const handleSelectPopover = option => {
    if (option === 'DRAWER') {
      setOpenDrawer(true);
    } else {
      setOpenChat(true);
    }

    setAnchorEl(null);
  };

  return (
    <TicketEditorWrapper>
      {!!useIcon && (
        <TicketButton onClick={e => (useChat ? setOpenPopover(true) : setOpenDrawer(true))}>
          <TicketIcon />
          <AddTicketIcon className="add-icon" />
        </TicketButton>
      )}

      {!!useChat && (
        <>
          <Popover open={isOpenPopover} className="popoverBody">
            <Typography className="popoverItem" onClick={() => handleSelectPopover('DRAWER')}>
              Create ticket
            </Typography>
            <Typography className="popoverItem" onClick={() => handleSelectPopover('CHAT')}>
              Chat with us
            </Typography>
          </Popover>
          {/* {openChat && <ChatBox onClose={() => setOpenChat(false)} />} */}
        </>
      )}
      <DrawerDetail
        open={openDrawer}
        onClose={() => handleClose(true, false)}
        menuProps={{
          items: [
            {
              icon: <Icon type="icon-ants-pencil" />,
              label: 'Settings',
              key: 'settings',
            },
          ],
          selectedKeys: ['settings'],
        }}
        headerProps={{
          showBorderBottom: true,
        }}
        name="ticket-editor-drawer"
      >
        {openDrawer && (
          <Content
            portalId={portalId}
            token={token}
            action={action}
            listUsers={listUsers}
            domainTicket={domainTicket}
            domainUpload={domainUpload}
            config={config}
            ticketDetails={ticketDetails}
            listComment={listComment}
            ticketId={ticketId}
            userId={userId}
            fetchgetTicketComments={fetchgetTicketComments}
            isLoadingComment={isLoadingComment}
            isLoadingDetails={isLoadingDetails}
            isLoadingDataSouce={isLoadingDataSouce}
            handleCloseDrawer={handleCloseDrawer}
            setIsSuccessCreated={setIsSuccessCreated}
            accountManage={accountManage}
            timeZone={timeZone}
            isLoadingSelectedAccount={isLoadingSelectedAccount}
            accountEdit={accountEdit}
            initOwnerId={ownerId}
            isAllAccount={isAllAccount}
            openDrawer={openDrawer}
            dataSelectInput={dataSelectInput}
            setDataSelectInput={setDataSelectInput}
          />
        )}
      </DrawerDetail>
      {/* <WrapperDrawer
        open={openDrawer}
        onClose={handleClose}
        anchor="right"
        style={{ zIndex: 1020 }}
        disableEnforceFocus
      >
        <Content
          title={action === 'create' ? 'Create a new ticket' : 'Ticket info'}
          handleClose={handleClose}
          portalId={portalId}
          token={token}
          action={action}
          listUsers={listUsers}
          domainTicket={domainTicket}
          config={config}
          ticketDetails={ticketDetails}
          listComment={listComment}
          ticketId={ticketId}
          userId={userId}
          fetchgetTicketComments={fetchgetTicketComments}
          isLoadingComment={isLoadingComment}
          isLoadingDetails={isLoadingDetails}
          isLoadingDataSouce={isLoadingDataSouce}
          handleCloseDrawer={handleCloseDrawer}
          setIsSuccessCreated={setIsSuccessCreated}
          accountManage={accountManage}
          timeZone={timeZone}
          isLoadingSelectedAccount={isLoadingSelectedAccount}
          accountEdit={accountEdit}
          initOwnerId={ownerId}
          isAllAccount={isAllAccount}
        />
      </WrapperDrawer> */}
      {/* <Message type="success" message="Success" isOpen={isSuccessCreated} /> */}
      <Modal open={isOpenModal} onCancel={() => setIsOpenModal(false)} className="modal">
        <div className="wrapperBody">
          <WrapperModalHeader>Confirm deletion of this ticket</WrapperModalHeader>
          <div className="modalBody">
            <span style={{ fontSize: '12px', color: '#000' }}>
              Once you confirm cancel, this ticket will NOT be saved.
            </span>
          </div>
          <div className="modalFooter">
            <Button
              style={{
                height: '28px',
                background: '#1f5fac',
                color: '#fff',
                fontSize: '12px',
                borderRadius: '3px',
                fontWeight: 'bold',
                textTransform: 'capitalize',
              }}
              onClick={handleConfirmModal}
            >
              Confirm
            </Button>
            <Button
              style={{
                height: '28px',
                background: '#fff',
                color: '#005fb8',
                fontSize: '12px',
                borderRadius: '3px',
                fontWeight: 'bold',
                textTransform: 'capitalize',
                border: '1px solid #edeef7',
              }}
              onClick={() => setIsOpenModal(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
      {contextHolder}
    </TicketEditorWrapper>
  );
};

TicketEditor.defaultProps = defaultProps;
