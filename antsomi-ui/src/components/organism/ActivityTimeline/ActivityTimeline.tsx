/* eslint-disable no-underscore-dangle */
import React, { useMemo } from 'react';
import { StyledNoData, StyldTitle, StyledRoot, TimelineBottom } from './styled';
import { ActivityTimeLineProps, Timeline } from './types';
import { Flex, Spin } from '../../atoms';
import { ItemEvent, TimeLineTitle, ItemGroupEvent } from './components';
import { useInView } from 'react-intersection-observer';
import { translate, translations } from '@antscorp/antsomi-locales';
import { isEmpty } from 'lodash';
import {
  differenceInMonths,
  formatUTCDateTZ,
  startOfMonth,
  subMonths,
} from '@antscorp/antsomi-ui/es/utils/date';
import { getPortalTimeZone } from '@antscorp/antsomi-ui/es/utils/portal';
import { EmptyData } from '../../molecules';
import clsx from 'clsx';

export const ActivityTimeline = (props: ActivityTimeLineProps) => {
  const {
    timelines = [],
    isLoading = false,
    title = translate(translations._BLOCK_TIMELINE_CUS),
    timezone = getPortalTimeZone(),
    objectName = '_THIS_PERSON_UPPERCASE',
    onFetchMore,
    eventTracking: eventTrackingProp = [],
    header,
    className,
  } = props;

  let { timelineAvailable } = props;

  timelineAvailable = (timelines.length > 0 && !!timelineAvailable) || false;

  const eventTracking = useMemo(() => {
    const list = [...eventTrackingProp];

    const map = Object.fromEntries(
      list.map(item => [`${item.eventCategoryId}@@${item.eventActionId}`, item]),
    );

    return { list, map };
  }, [eventTrackingProp]);

  const [bottomRef] = useInView({
    threshold: 0.7,
    onChange: inView => {
      if (!inView || isLoading || !timelineAvailable || !onFetchMore) return;

      onFetchMore();
    },
  });

  const showBottomComponent = () => {
    if (isEmpty(timelines) || isLoading) return null;

    return (
      <TimelineBottom align="center" justify="center">
        {timelineAvailable && onFetchMore ? (
          <Flex ref={bottomRef}>
            <Spin indicatorSize={24} />
          </Flex>
        ) : (
          translate(translations._INFO_NO_MORE_ACTIVITY, 'No more activities!')
        )}
      </TimelineBottom>
    );
  };

  const showMainContent = (timelines: Timeline[], customerName: string) => {
    const result: React.ReactNode[] = [];

    if (timelines.length > 0) {
      const startDate = startOfMonth(new Date()).getTime();

      let firstDateEvent = startOfMonth(
        new Date(formatUTCDateTZ(timelines[0].trackedDateTimeUTC, 'MM/DD/YYYY', timezone)),
      ).getTime();

      let diffTempt = differenceInMonths(new Date(startDate), new Date(firstDateEvent));

      if (diffTempt === 0) {
        result.push(
          <TimeLineTitle
            key={`time-heading--${startDate}`}
            type="label"
            data={{ date: firstDateEvent }}
          />,
        );
      } else if (diffTempt === 1) {
        result.push(
          <TimeLineTitle
            key={`time-heading--${startDate}`}
            type="nodata-single"
            data={{ date: startDate }}
          />,
        );

        result.push(
          <TimeLineTitle
            key={`time-heading--v2-${firstDateEvent}`}
            type="label"
            data={{ date: firstDateEvent }}
          />,
        );
      } else {
        const dateNextMonthEvent = startOfMonth(subMonths(new Date(firstDateEvent), -1)).getTime();

        result.push(
          <TimeLineTitle
            key={`time-heading--${startDate}`}
            type="nodata-multi"
            data={{ startDate, endDate: dateNextMonthEvent }}
          />,
        );

        result.push(
          <TimeLineTitle
            key={`time-heading--v2-${firstDateEvent}`}
            type="label"
            data={{ date: firstDateEvent }}
          />,
        );
      }

      timelines.forEach((item, index) => {
        const firstDateEventTempt = startOfMonth(
          new Date(formatUTCDateTZ(item.trackedDateTimeUTC, 'MM/DD/YYYY', timezone)),
        ).getTime();

        if (firstDateEvent !== firstDateEventTempt) {
          diffTempt = differenceInMonths(new Date(firstDateEvent), new Date(firstDateEventTempt));

          if (diffTempt === 0) {
            result.push(
              <TimeLineTitle
                key={`time-heading--${item.timestamp}`}
                type="label"
                data={{ date: firstDateEventTempt }}
              />,
            );
          } else if (diffTempt === 1) {
            result.push(
              <TimeLineTitle
                key={`time-heading--${item.timestamp}`}
                type="label"
                data={{ date: firstDateEventTempt }}
              />,
            );
          } else if (diffTempt === 2) {
            const dateNextMonthEvent = startOfMonth(
              subMonths(new Date(firstDateEventTempt), -1),
            ).getTime();

            result.push(
              <TimeLineTitle
                key={`time-heading--${item.timestamp}`}
                type="nodata-single"
                data={{ date: dateNextMonthEvent }}
              />,
            );

            result.push(
              <TimeLineTitle
                key={`time-heading-v2-${item.timestamp}`}
                type="label"
                data={{ date: firstDateEventTempt }}
              />,
            );
          } else {
            const datePrevMonthEvent = startOfMonth(
              subMonths(new Date(firstDateEvent), 1),
            ).getTime();

            const dateNextMonthEvent = startOfMonth(
              subMonths(new Date(firstDateEventTempt), -1),
            ).getTime();

            result.push(
              <TimeLineTitle
                key={`time-heading--${item.timestamp}`}
                type="nodata-multi"
                data={{
                  startDate: datePrevMonthEvent,
                  endDate: dateNextMonthEvent,
                }}
              />,
            );

            result.push(
              <TimeLineTitle
                key={`time-heading-v2-${item.timestamp}`}
                type="label"
                data={{ date: firstDateEventTempt }}
              />,
            );
          }
          firstDateEvent = firstDateEventTempt;
        }

        const key = `${item.etype}-${item.timestamp}-${item.sectionKey}-${index}`;

        if (item.data.totalActivityCount === 1) {
          result.push(
            <ItemEvent
              keyItem={key}
              item={item}
              objectName={customerName}
              eventTracking={eventTracking}
              key={key}
            />,
          );
        } else if (item.data.totalActivityCount > 1) {
          result.push(
            <ItemGroupEvent
              keyItem={key}
              item={item}
              objectName={customerName}
              eventTracking={eventTracking}
              key={key}
            />,
          );
        }
      });
    }

    return result;
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <>
          {header}
          <Flex style={{ height: 240 }} align="center" justify="center">
            <Spin indicatorSize={24} />
          </Flex>
        </>
      );
    }

    if (!timelines.length) {
      return (
        <>
          {header}
          <StyledNoData align="center" justify="center">
            <EmptyData
              showIcon={false}
              title={translate(translations._PROFILES_NO_DATA)}
              description={translate(translations._PROFILES_MESSAGE_NO_DATA_TIMELINE).toString()}
            />
          </StyledNoData>
        </>
      );
    }

    return (
      <>
        {header}
        {showMainContent(timelines, objectName)}
        {showBottomComponent()}
      </>
    );
  };

  return (
    <StyledRoot className={clsx(className, 'activity-timeline')}>
      <StyldTitle>{title}</StyldTitle>

      {renderContent()}
    </StyledRoot>
  );
};
