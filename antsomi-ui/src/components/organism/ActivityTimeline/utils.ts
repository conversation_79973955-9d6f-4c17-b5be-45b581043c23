/* eslint-disable no-underscore-dangle */
import { DATA_EVENT, mapTranslateCode } from './constants';
import { EventTrackingWithMap } from './types';
import { get } from 'lodash';
import { AppConfigProviderProps, ENV } from '../../..';
import { translate, translations } from '@antscorp/antsomi-ui/es/locales';
import { safeParse } from '@antscorp/antsomi-ui/es/utils';
import { formatDate, formatUTCDateTZ } from '@antscorp/antsomi-ui/es/utils/date';

import {
  getPortalFormatDateTimeLong,
  getPortalTimeZone,
} from '@antscorp/antsomi-ui/es/utils/portal';
import { CDP_ROUTE } from '@antscorp/antsomi-ui/es/constants';

const translateAt = translate(translations._INFO_LOCATION, 'at');

const urlJourney = (data: Record<string, any>, config: AppConfigProviderProps) => {
  const { story = {} } = data;

  const url = `${CDP_ROUTE[config.env || ENV.PROD]}/gen2/${config.auth?.portalId}/${config.auth?.userId}/marketing-hub/journeys/${
    story.channelId && story.channelId
  }/list?ui=detail-drawer&journeyId=${story.id && story.id}&channelId=${
    story.channelId && story.channelId
  }&tab=settings&design=preview`;

  return url;
};

export const getValuesReplace = (objectReplace: any[], data: Record<string, any>) => {
  const values = {};

  if ((objectReplace || []).length === 0 || Object.keys(data).length === 0) {
    return values;
  }

  objectReplace.forEach((item: any) => {
    if (item !== 'product_name' && item !== 'page_name') {
      if (item === 'x_point' || item === 'store_link') {
        if (item === 'store_link') {
          values[item] =
            `${translateAt} <strong style="color: rgb(239, 51, 63);">${data[item]}</strong>`;
        } else {
          values[item] = `<strong style="color: rgb(239, 51, 63);">${data[item]}</strong>`;
        }
      } else {
        values[item] = data[item] !== undefined ? `<strong>${data[item]}</strong>` : '';
      }
    } else {
      values[item] = data[item];
    }
  });

  return values;
};

const parseUrl = (params: { name?: string }) => {
  const { name = 'N/A' } = params;

  const validateName = name.replace(/https|http|www|\/\/|:/gi, '');

  if (validateName !== 'N/A') {
    return validateName;
  }

  return name;
};

export const getStringHtmlByType = (item: Record<string, any>, eType = 'product') => {
  const validateDate = item || {};

  if (Object.keys(validateDate).length === 0) {
    return `<span class="title-no-url">N/A</span>`;
  }

  const label = validateDate.name || 'N/A';

  const url = (eType === 'product' ? validateDate.page_url : validateDate.url) || '';

  if (url?.length === 0) {
    return `<span class="title-no-url">${label}</span>`;
  }

  if (label === 'N/A') {
    return `<a class="title-have-url" href=${encodeURI(url)} target="_blank">${parseUrl({ name: url })}</a>`;
  }

  return `<a class="title-have-url" href=${url} target="_blank">${url}</a>`;
};

const getStoreLinkAndNumberProduct = (
  validateExtra: Record<string, any>,
  itemEvent: Record<string, any>,
) => {
  let num_of_products = 'N/A';
  let store_link = 'N/A';

  if (Object.keys(validateExtra).length === 0) {
    num_of_products = itemEvent.items.length || 'N/A';
    store_link = '';
  } else {
    num_of_products = itemEvent.items.length || 'N/A';

    if (Object.keys(validateExtra.store || {}).length === 0) {
      store_link = '';
    } else {
      const storeName = validateExtra.store.name || '';
      const storeType = validateExtra.store.store_type_name || '';
      const storeUrl = validateExtra.store.store_url || '';

      if (storeUrl.length > 0) {
        if (storeName.length === 0) {
          store_link = `<a class="title-have-url" href=${storeUrl} target="_blank">${storeType}</a>`;
        } else if (storeType.length === 0) {
          store_link = `<a class="title-have-url" href=${storeUrl} target="_blank">${storeName}</a>`;
        } else {
          store_link = `<a class="title-have-url" href=${storeUrl} target="_blank">${storeName} ${storeType}</a>`;
        }
      } else {
        if (storeName.length === 0) {
          store_link = `<strong>${storeType}</strong>`;
        } else if (storeType.length === 0) {
          store_link = `<strong>${storeName}</strong>`;
        } else {
          store_link = `<strong>${storeName} ${storeType}</strong>`;
        }
      }
    }
  }

  return { num_of_products, store_link };
};

export const getProductName = (data: any[]) => {
  let productNames = 'N/A';
  const arrayProductName: any[] = [];
  const validateData = data || [];

  if (validateData.length === 0) {
    return productNames;
  }

  validateData.forEach((item: any) => {
    if (item.name) {
      arrayProductName.push(item.name);
    }
  });
  productNames = arrayProductName.join(', ');
  return productNames;
};

export const getInfoEvent = (
  eType: string,
  itemEvent: Record<string, any>,
  eventTracking: EventTrackingWithMap,
  customerName: string,
  config: AppConfigProviderProps,
) => {
  const keyTitleEvent = `${itemEvent.eventCategoryId}@@${itemEvent.eventActionId}`;

  const validateExtra = itemEvent.extra || {};

  const promotionCodes = itemEvent.items
    .filter((item: any) => item.item_type === 'promotion_code')
    .map((item: any) => item.name || item.id)
    .join(', ');

  let titleEvent = `${itemEvent.pageType} ${eType}`;

  let infoEvent = DATA_EVENT.map[titleEvent];

  titleEvent = `${titleEvent}${
    promotionCodes.length > 0
      ? `: <strong style="color: rgb(239, 51, 63);">${promotionCodes}</strong>`
      : ''
  }`;

  const data: Record<string, any> = {};

  data.event_name = titleEvent;

  if (
    eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'promotion_code_sent_tracking' ||
    eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'promotion_code_used_tracking'
  ) {
    data.event_name = `<strong style="color: rgb(239, 51, 63);">${promotionCodes}</strong>`;
  }

  data.customer_name =
    customerName === '_THIS_PERSON_UPPERCASE'
      ? translate(translations._THIS_PERSON_UPPERCASE, 'This user')
      : customerName;

  if (eventTracking.map[keyTitleEvent]) {
    titleEvent = eventTracking.map[keyTitleEvent].translateLabel;
  }

  if (validateExtra.store !== undefined) {
    const { store_link } = getStoreLinkAndNumberProduct(validateExtra, itemEvent);
    data.store_link = store_link;
  }

  // ETYPE === PRODUCT
  if (eType === 'product') {
    let listProductName = 'N/A';
    listProductName = getProductName(itemEvent.items);
    data.product_name = listProductName;

    if (itemEvent.pageType === 'order_completed') {
      // PAGETYPE ORDER_COMPLETE
      data.order_id = validateExtra.order_id || 'N/A';
    } else if (itemEvent.pageType === 'purchase') {
      // PAGETYPE PURCHASE

      data.order_id = validateExtra.order_id || 'N/A';

      data.num_of_products = itemEvent.items.length || 'N/A';
    } else if (itemEvent.pageType === 'search') {
      // PAGETYPE SEARCH

      data.keywords = validateExtra.src_search_term || 'N/A';
    } else if (itemEvent.pageType === 'product_list_view') {
      // PAGETYPE PRODUCT_LIST_VIEW

      data.product_list = getProductName(itemEvent.items);
    } else if (
      itemEvent.pageType === 'click' ||
      itemEvent.pageType === 'view' ||
      itemEvent.pageType === 'add_to_cart'
    ) {
      // PAGETYPE CLICK OR VIEW
      // eslint-disable-next-line no-console
      console.log('itemEvent.items[0]', itemEvent);

      data.product_name = getStringHtmlByType(itemEvent.items[0], eType);
    }
  } else if (eType === 'order') {
    // PAGETYPE ORDER

    data.order_id = safeParse(validateExtra.order_id, 'N/A');
  } else if (eType === 'transaction') {
    // ETYPE TRANSACTION

    if (itemEvent.pageType === 'purchase') {
      data.order_id = safeParse(validateExtra.order_id, 'N/A');
      const { num_of_products } = getStoreLinkAndNumberProduct(validateExtra, itemEvent);
      data.num_of_products = num_of_products;
    } else {
      // PAGETYPE PURCHASE_OFFLINE
      data.transaction_id = safeParse(validateExtra.transaction_id, 'N/A');
      const { num_of_products, store_link } = getStoreLinkAndNumberProduct(
        validateExtra,
        itemEvent,
      );
      data.num_of_products = num_of_products;
      data.store_link = store_link;
    }
  } else if (eType === 'page' || eType === 'pageview') {
    // ETYPE PAGE OR PAGEVIEW

    if (itemEvent.pageType === 'view') {
      // PAGETYPE VIEW

      data.page_name = getStringHtmlByType(itemEvent.items[0], eType);
    }
  } else if (eType === 'ticket') {
    // ETYPE TICKET

    if (itemEvent.pageType === 'send') {
      // PAGETYPE SEND

      data.array_ticket_names = getProductName(itemEvent.items);
    }
  } else if (eType === 'conversation') {
    // ETYPE CONVERSATION

    if (itemEvent.pageType === 'chat') {
      // PAGETYPE CHAT

      data.channel_name = itemEvent.items[0].category || 'N/A';
    }
  } else if (eType === 'user') {
    // ETYPE USER

    if (itemEvent.pageType === 'identify' || itemEvent.pageType === 'reset_anonymous_id') {
      // PAGETYPE IDENTIFY OR RESET_ANONYMUS_ID

      data.user_id = itemEvent.userId;
    } else if (itemEvent.pageType === 'redeem_point' || itemEvent.pageType === 'earn_point') {
      // PAGETYPE REDEEM_POINT OR EARN_POINT

      data.x_point = validateExtra.point || 'N/A';
      data.transaction_id = validateExtra.transaction_id || 'N/A';

      const { store_link } = getStoreLinkAndNumberProduct(validateExtra, itemEvent);
      data.store_link = store_link;
    }
  } else if (eType === 'advertising') {
    // ETYPE ADVERTISING

    if (
      itemEvent.pageType === 'impression' ||
      itemEvent.pageType === 'viewable' ||
      itemEvent.pageType === 'click'
    ) {
      // PAGETYPE IMPRESSION , VIEWABLE, CLICK
      data.campaign_name = safeParse(validateExtra.utm_campaign, 'N/A');
    }
  } else if (eType === 'browsing') {
    if (itemEvent.pageType === 'product_search') {
      data.keyword = safeParse(validateExtra.src_search_term, '');
    }
  } else if (eType === 'evoucher') {
    if (itemEvent.pageType === 'received') {
      data.x_code = safeParse(validateExtra.coupon_code, 'N/A');
      data.campaign_name = safeParse((validateExtra.campaign || {}).name, 'N/A');
    }
  } else if (eType === 'store') {
    if (itemEvent.pageType === 'visit') {
      data.step_section = itemEvent.extra.step_section;
    }
  } else if (eType === 'screenview') {
    if (itemEvent.pageType === 'view' && validateExtra.screen_type) {
      data.screen_type = validateExtra.screen_type;
    }
  }

  let valuesReplace: Record<string, any> = {};

  if (infoEvent) {
    infoEvent.iconName = get(eventTracking, `map.${keyTitleEvent}.iconUrl`);
    infoEvent.backgroundIcon = '#005fd8';

    valuesReplace = getValuesReplace(infoEvent.objectReplace, data);
  } else {
    infoEvent = {
      translateCode: '_EVENT_DES_PERFORM_EVENT',
      iconCode: '_EVENT_UNDEFINED',
      showSlideImageAndPrice: false,
      showQuantity: false,
      showRevenue: false,
      objectReplace: ['customer_name', 'event_name'],
      backgroundIcon: '#005fd8',
      iconName: eventTracking.map[keyTitleEvent]?.iconUrl,
      showRedeem: false,
    };

    const { story = {} } = validateExtra;
    // data.event_name = titleEvent;
    valuesReplace = getValuesReplace(infoEvent.objectReplace, data);

    if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'promotion_code_sent_tracking') {
      valuesReplace.promotion_code = valuesReplace.event_name;
      delete valuesReplace.event_name;
      valuesReplace.journey_name = `<a  class="title-have-url" href=${urlJourney(
        validateExtra,
        config,
      )} target="_blank">${story.storyName}</a>`;
      if (
        validateExtra.channel &&
        mapTranslateCode.promotion_code_sent_tracking[parseInt(validateExtra.channel.id)]
      ) {
        infoEvent.translateCode =
          mapTranslateCode.promotion_code_sent_tracking[
            parseInt(validateExtra.channel.id)
          ].translateLabel;
      }
    } else if (
      eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'promotion_code_used_tracking'
    ) {
      valuesReplace.promotion_code = valuesReplace.event_name;
      delete valuesReplace.event_name;
      infoEvent.translateCode = translations._EVENT_CODE_USED_SMS;
    } else if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'viewable_advertising') {
      valuesReplace.journey_name = `<a  class="title-have-url" href=${urlJourney(
        validateExtra,
        config,
      )} target="_blank">${story.storyName}</a>`;
      delete valuesReplace.campaign_name;
      if (
        validateExtra.channel &&
        mapTranslateCode.viewable_advertising[parseInt(validateExtra.channel.id)]
      ) {
        infoEvent.translateCode =
          mapTranslateCode.viewable_advertising[parseInt(validateExtra.channel.id)].translateLabel;
      }
    } else if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'impression_advertising') {
      valuesReplace.journey_name = `<a class="title-have-url" href=${urlJourney(
        validateExtra,
        config,
      )} target="_blank">${story.storyName}</a>`;

      delete valuesReplace.campaign_name;

      if (
        validateExtra.channel &&
        mapTranslateCode.impression_advertising[parseInt(validateExtra.channel.id)]
      ) {
        infoEvent.translateCode =
          mapTranslateCode.impression_advertising[
            parseInt(validateExtra.channel.id)
          ].translateLabel;
      }
    } else if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'click_advertising') {
      valuesReplace.journey_name = `<a class="title-have-url" href=${urlJourney(
        validateExtra,
        config,
      )} target="_blank">${story.storyName}</a>`;
      delete valuesReplace.campaign_name;
      if (
        validateExtra.channel &&
        mapTranslateCode.click_advertising[parseInt(validateExtra.channel.id)]
      ) {
        infoEvent.translateCode =
          mapTranslateCode.click_advertising[parseInt(validateExtra.channel.id)].translateLabel;
      }
    } else if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'sent_tracking') {
      valuesReplace.journey_name = `<a class="title-have-url" href=${urlJourney(
        validateExtra,
        config,
      )} target="_blank">${story.storyName}</a>`;
      delete valuesReplace.campaign_name;
      if (
        validateExtra.channel &&
        mapTranslateCode.sent_tracking[parseInt(validateExtra.channel.id)]
      ) {
        infoEvent.translateCode =
          mapTranslateCode.sent_tracking[parseInt(validateExtra.channel.id)].translateLabel;
      }
    } else if (eventTracking.map[keyTitleEvent]?.eventTrackingCode === 'journey_bo_update_info') {
      valuesReplace.journey_name = `<strong>${story.storyName}</strong>`;
      delete valuesReplace.campaign_name;
      infoEvent.translateCode = translations._EVENT_BO_UPDATE;
    }
  }

  let fullContentEvent = translate(infoEvent.translateCode, titleEvent, valuesReplace);

  if (infoEvent.objectReplace.includes('store_link') === false && data.store_link !== undefined) {
    fullContentEvent += ` ${translateAt} <strong style="color: rgb(239, 51, 63);">${data.store_link}</strong>`;
  } else if (data.step_section) {
    fullContentEvent += ` ${translateAt} <strong style="color: rgb(239, 51, 63);">${
      data.step_section
    }</strong>`;
  }

  let perShow = 2;

  if (itemEvent.items.length > 0 && itemEvent.items[0].item_type === 'product') {
    perShow = itemEvent.items.length < 2 ? itemEvent.items.length : 2;
  }

  let limitShowSlide = itemEvent.items.length;

  if (infoEvent.limitShowSlide !== 'full') {
    if (infoEvent.limitShowSlide <= limitShowSlide) {
      limitShowSlide = infoEvent.limitShowSlide;
    }
  }

  return {
    titleEvent,
    fullContentEvent,
    infoEvent,
    validateExtra,
    perShow,
    limitShowSlide,
  };
};

export function formatDateHeader(timestamp: string | Date, timezone: string = getPortalTimeZone()) {
  let value: string = '';

  try {
    if (typeof timestamp === 'string') {
      value = formatUTCDateTZ(timestamp, getPortalFormatDateTimeLong(), timezone);
    } else {
      value = formatUTCDateTZ(timestamp, getPortalFormatDateTimeLong(), timezone);
    }

    return value;
  } catch (error) {
    return '';
  }
}

export const checkShowRedeem = (data: Record<string, any>) => {
  const validateData = safeParse(data, {});

  if (Object.keys(validateData).length === 0) {
    return false;
  }

  const point = safeParse(validateData.point, 'N/A');
  const pointType = safeParse(validateData.point_type, 'N/A');

  if (point === 'N/A' || pointType === 'N/A') {
    return false;
  }

  return true;
};

export function formatDateTitleHeading(timestamp: string | number) {
  return formatDate(new Date(timestamp), 'MMMM YYYY');
}
