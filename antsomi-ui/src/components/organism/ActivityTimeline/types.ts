export type Timeline = Record<string, any>;

export type EventTracking = Record<string, any>;

export type EventTrackingWithMap = {
  map: Record<string, EventTracking>;
  list: EventTracking[];
};

export type ActivityTimeLineProps = Partial<{
  isLoading: boolean;
  timelines: Timeline[];
  timelineAvailable: boolean;
  timezone: string;
  title: string;
  objectName: string;
  eventTracking: EventTracking[];
  onFetchMore: () => void;
  longDateTimeFormat: string;
  header: React.ReactNode;
  className: string;
}>;
