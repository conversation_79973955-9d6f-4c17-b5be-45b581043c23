/* eslint-disable no-underscore-dangle */
import React from 'react';
import { checkShowRedeem, formatDateHeader, getInfoEvent } from '../../utils';
import { EventTrackingWithMap } from '../../types';
import { TimelineSection } from '../TimelineSection/TimelineSection';
import htmlParse from 'html-react-parser';
import { TimelineContainer, WrapperContainerRedeem } from '../../styled';
import { CardMember } from '../CardMember';
import { Redeem } from '../Redeem';
import { ListItemContent } from './styled';
import { ActionHistory } from '../ActionHistory';
import { ProductCard } from '../ProductCard';
import { isEmpty } from 'lodash';
import { ListItem, ListItemTime, ListItemTimeLine, ListItemTitle } from '../ListItem';
import { List } from '../List';
import {
  getPortalCurrency,
  getPortalTimeZone,
  safeParseDisplayFormatCurrency,
} from '@antscorp/antsomi-ui/es/utils/portal';
import { translate, translations } from '@antscorp/antsomi-ui/es/locales';
import { useAppConfigContext } from '@antscorp/antsomi-ui/es/providers';
import { numberWithCommas, safeParse, validateImageURL } from '@antscorp/antsomi-ui/es/utils';
import 'swiper/swiper-bundle.css';
import { Slide } from '../Slide';

type ItemGroupEventProps = Partial<{
  keyItem: string;
  item: Record<string, any>;
  objectName: string;
  currency: string;
  eventTracking: EventTrackingWithMap;
  timezone?: string;
}>;

export const renderProductCards = (
  dataItem: Record<string, any>,
  limitShow: number,
  inforEvent: Record<string, any>,
  infoCurrency: string,
  key: string,
) => {
  const result: React.ReactNode[] = [];

  dataItem.items.forEach((itemSlide: Record<string, any>, indexSlide: number) => {
    if (indexSlide < limitShow) {
      const productPrice = safeParse(itemSlide.sale_price, '--');
      let isImgUrl = true;
      let productUrl: string | null = null;

      if (safeParse(itemSlide.page_url, '').length > 0) {
        productUrl = safeParse(itemSlide.page_url, '').length === 0 ? '' : itemSlide.page_url;
      }

      if (validateImageURL(safeParse(itemSlide.image_url, '')) === false) {
        isImgUrl = false;
      }

      result.push(
        <ProductCard
          title={safeParse(itemSlide.name, 'N/A')}
          key={`${indexSlide.toString()} - safeParse(itemSlide.name, 'N/A')}-${key}`}
          quantity={inforEvent.showQuantity ? safeParse(itemSlide.item_quantity, 'N/A') : null}
          href={safeParse(productUrl, '').length > 0 ? productUrl : ''}
          price={`${
            productPrice === '--' ? '--' : `${numberWithCommas(productPrice)} ${infoCurrency}`
          }  `}
          urlImg={
            isImgUrl
              ? itemSlide.image_url
              : 'https://e.antsomi.com/cdp/default/ui-images/info/no-image-available.png'
          }
        />,
      );
    }
  });

  return result;
};

export const ItemGroupEvent = (props: ItemGroupEventProps) => {
  const { appConfig } = useAppConfigContext();

  const {
    keyItem: key = '',
    item = {},
    objectName = '',
    eventTracking = { map: {}, list: [] },
    currency = getPortalCurrency(),
    timezone = getPortalTimeZone(),
  } = props;

  const { etype: eType } = item;

  const renderGroupEvent = (dataEvent: Record<string, any>) => {
    const dataActivity = dataEvent.data.firstActivities;

    const { titleEvent: title, infoEvent: infoEventLast } = getInfoEvent(
      eType,
      dataActivity[0],
      eventTracking,
      objectName,
      appConfig,
    );

    const rearTitle = translate(
      translations._EVENT_DES_PERFORM_OTHER_EVENT,
      'and performed other {{x}} actions',
      { x: dataActivity.length - 1 },
    );

    if (isEmpty(dataActivity)) return null;

    return (
      <TimelineSection
        title={`${title}, ${rearTitle}`}
        bgIcon={infoEventLast.backgroundIcon}
        iconName={infoEventLast.iconName ? infoEventLast.iconName : 'cus web'}
      >
        <List type="timeline" loadMore limit={5} noPaddingBottom>
          {dataActivity.map((itemEvent: Record<string, any>, index: number) => {
            const {
              titleEvent,
              fullContentEvent = '',
              infoEvent,
              validateExtra,
              perShow,
              limitShowSlide,
            } = getInfoEvent(eType, itemEvent, eventTracking, objectName, appConfig);

            return (
              <React.Fragment key={`${titleEvent}-${fullContentEvent}-${index.toString()}`}>
                <ListItem>
                  <ListItemTimeLine>
                    <ListItemTitle ellipsis={{ tooltip: true }}>
                      {htmlParse(fullContentEvent)}
                    </ListItemTitle>
                  </ListItemTimeLine>

                  <ListItemTime ellipsis={{ tooltip: true }}>
                    {formatDateHeader(itemEvent.trackedDateTimeUTC, timezone)}
                  </ListItemTime>
                </ListItem>

                {infoEvent.showRedeem && validateExtra.loyalty_card ? (
                  <WrapperContainerRedeem>
                    <CardMember info={validateExtra.loyalty_card} />

                    {checkShowRedeem(validateExtra) && (
                      <Redeem
                        point={safeParse(validateExtra.point, 'N/A')}
                        redeemType={safeParse(validateExtra.point_type, 'N/A')}
                      />
                    )}
                  </WrapperContainerRedeem>
                ) : (
                  infoEvent.showRedeem &&
                  checkShowRedeem(validateExtra) && (
                    <Redeem
                      point={safeParse(validateExtra.point, 'N/A')}
                      redeemType={safeParse(validateExtra.point_type, 'N/A')}
                    />
                  )
                )}

                {itemEvent.items.length > 0 && itemEvent.items[0].item_type === 'product' && (
                  <ListItemContent>
                    <ActionHistory
                      revenue={
                        infoEvent.showRevenue
                          ? `${
                              safeParse(validateExtra.revenue, '--') === '--'
                                ? '--'
                                : safeParseDisplayFormatCurrency(validateExtra.revenue)
                            } `
                          : null
                      }
                    >
                      <Slide
                        slidesPerView={perShow}
                        items={renderProductCards(
                          itemEvent,
                          limitShowSlide,
                          infoEvent,
                          currency,
                          key,
                        )}
                      />
                    </ActionHistory>
                  </ListItemContent>
                )}
              </React.Fragment>
            );
          })}
        </List>
      </TimelineSection>
    );
  };

  return (
    <TimelineContainer className="item-group-event" key={key}>
      {renderGroupEvent(item)}
    </TimelineContainer>
  );
};
