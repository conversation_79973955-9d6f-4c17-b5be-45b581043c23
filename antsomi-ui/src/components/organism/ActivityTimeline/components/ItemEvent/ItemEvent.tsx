import React from 'react';
import { checkShowRedeem, formatDateHeader, getInfoEvent } from '../../utils';
import { useAppConfigContext } from '@antscorp/antsomi-ui/es/providers';
import { TimelineSection } from '../TimelineSection/TimelineSection';
import htmlParse from 'html-react-parser';
import { TimelineContainer, WrapperContainerRedeem } from '../../styled';
import { CardMember } from '../CardMember';
import { safeParse } from '@antscorp/antsomi-ui/es/utils';
import { Redeem } from '../Redeem';
import { ListItemContent } from '../ItemGroupEvent/styled';
import { ActionHistory } from '../ActionHistory';
import { renderProductCards } from '../ItemGroupEvent/ItemGroupEvent';
import {
  getPortalCurrency,
  safeParseDisplayFormatCurrency,
} from '@antscorp/antsomi-ui/es/utils/portal';
import { ListItem, ListItemTime, ListItemTimeLine, ListItemTitle } from '../ListItem';
import { List } from '../List';
import { EventTrackingWithMap } from '../../types';
import { Slide } from '../Slide';

type ItemEventProps = Partial<{
  item: Record<string, any>;
  objectName: string;
  eventTracking: EventTrackingWithMap;
  keyItem: string;
  currency: string;
}>;

export const ItemEvent = (props: ItemEventProps) => {
  const {
    keyItem: key = '',
    item = {},
    objectName = '',
    eventTracking = { list: [], map: {} },
    currency = getPortalCurrency(),
  } = props;

  const { appConfig } = useAppConfigContext();

  const { etype: eType } = item;

  const renderSingleEvent = (dataEvent: Record<string, any>) => {
    const dataActivity = dataEvent.data.firstActivities;

    const {
      titleEvent: title,
      fullContentEvent = '',
      infoEvent,
      validateExtra,
      perShow,
      limitShowSlide,
    } = getInfoEvent(eType, dataActivity[0], eventTracking, objectName, appConfig);

    return (
      <TimelineSection
        title={title}
        bgIcon={infoEvent.backgroundIcon}
        iconName={infoEvent.iconName}
      >
        <List type="timeline">
          <ListItem>
            <ListItemTimeLine>
              <ListItemTitle ellipsis={{ tooltip: true }}>
                {htmlParse(fullContentEvent)}
              </ListItemTitle>
            </ListItemTimeLine>

            <ListItemTime ellipsis={{ tooltip: true }}>
              {formatDateHeader(dataActivity[0].trackedDateTimeUTC)}
            </ListItemTime>
          </ListItem>
        </List>

        {infoEvent.showRedeem && validateExtra.loyalty_card ? (
          <WrapperContainerRedeem>
            <CardMember info={validateExtra.loyalty_card} />
            {checkShowRedeem(validateExtra) && (
              <Redeem
                point={safeParse(validateExtra.point, 'N/A')}
                redeemType={safeParse(validateExtra.point_type, 'N/A')}
              />
            )}
          </WrapperContainerRedeem>
        ) : (
          infoEvent.showRedeem &&
          checkShowRedeem(validateExtra) && (
            <Redeem
              point={safeParse(validateExtra.point, 'N/A')}
              redeemType={safeParse(validateExtra.point_type, 'N/A')}
            />
          )
        )}

        {dataActivity[0].items.length > 0 && dataActivity[0].items[0].item_type === 'product' && (
          <ListItemContent>
            <ActionHistory
              revenue={
                infoEvent.showRevenue
                  ? `${
                      safeParse(validateExtra.revenue, '--') === '--'
                        ? '--'
                        : safeParseDisplayFormatCurrency(validateExtra.revenue)
                    } `
                  : null
              }
            >
              <Slide
                slidesPerView={perShow}
                items={renderProductCards(
                  dataActivity[0],
                  limitShowSlide,
                  infoEvent,
                  currency,
                  key,
                )}
              />
            </ActionHistory>
          </ListItemContent>
        )}
      </TimelineSection>
    );
  };

  return (
    <TimelineContainer className="item-event" key={key}>
      {renderSingleEvent(item)}
    </TimelineContainer>
  );
};
