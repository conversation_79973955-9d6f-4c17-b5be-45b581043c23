import React from 'react';
import { DateTitle, TimelineTitleRoot } from './styled';
import { formatDateTitleHeading } from '../../utils';

type TimeLineTitleProps = Partial<{
  title: string;
  type: string;
  data: Record<string, any>;
}>;

const showContent = (type: string, data: Record<string, any>) => {
  if (type === 'nodata-single') {
    return null;
  }

  if (type === 'nodata-multi') {
    return null;
  }

  return <DateTitle ellipsis={{ tooltip: true }}>{formatDateTitleHeading(data.date)}</DateTitle>;
};

export const TimeLineTitle = (props: TimeLineTitleProps) => {
  const { type = '', data = {} } = props;

  const content = showContent(type, data);

  if (!content) return null;

  return <TimelineTitleRoot>{showContent(type, data)}</TimelineTitleRoot>;
};
