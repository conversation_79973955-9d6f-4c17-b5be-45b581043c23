// Libraries
import { <PERSON>a, StoryObj } from '@storybook/react';
import { useEffect, useMemo, useState } from 'react';

// Components
import { MatchesAnySelect, Typography } from '../..';
import { recursiveSearchItems } from '../../../utils';
import { HelpIcon, LongerIcon } from '../../icons';
import { EXTRA_VALUE_SAMPLE_DATA, SAMPLE_DATA } from './constants';
import { MatchesAnyItem, MatchesAnySelectProps } from './types';

import { countryItems } from './mock';

const meta: Meta<typeof MatchesAnySelect> = {
  component: MatchesAnySelect,
  title: 'Molecules/MatchesAnySelect',
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'MatchesAnySelect Component',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof MatchesAnySelect>;

type TSegmentType = {
  segmentSize: string;
  rowCount: string;
  status: number;
};

type TState = {
  selectedItems: MatchesAnyItem<TSegmentType>[];
  items: MatchesAnyItem<TSegmentType>[];
  searchValue?: string;
};
const RenderMatchesAnySelectDefault = (args: MatchesAnySelectProps) => {
  const [state, setState] = useState<any>({
    selectedItems: [],
    items: [],
    loading: true,
  });

  useEffect(() => {
    setTimeout(() => {
      setState(prev => ({ ...prev, items: SAMPLE_DATA, loading: false }));
    }, 5000);
  }, []);

  return (
    <MatchesAnySelect
      items={state.items}
      selectedItems={state.selectedItems}
      loading={state.loading}
      onChange={selectedItems => setState(prev => ({ ...prev, selectedItems }))}
      {...args}
    />
  );
};

const RenderMatchesAnyWithGroupSelection = (args: MatchesAnySelectProps) => {
  const [state, setState] = useState<any>({
    selectedItems: [],
    items: [],
    loading: false,
    groupSelect: {
      options: [],
    },
  });
  const { groupSelect } = state;

  useEffect(() => {
    (async () => {
      const response = await fetch('https://restcountries.com/v3.1/all');
      const data = await response.json();

      const groupItems = data.splice(0, 10).map(item => ({
        key: item.cca2,
        label: item.name?.common,
      }));

      const items = await Promise.all(
        groupItems.map(async groupItem => {
          const response = await fetch(
            `https://secure.geonames.org/searchJSON?country=${groupItem.key}&featureClass=P&maxRows=10&username=nltruongvi1999`,
          );

          const data = await response.json();

          return {
            key: groupItem.key,
            title: groupItem.label,
            children:
              data?.geonames?.map(item => ({
                key: item.geonameId,
                title: item.toponymName,
              })) || [],
          };
        }),
      );

      setState(prev => ({
        ...prev,
        loading: false,
        items: items.filter(Boolean),
        groupSelect: {
          ...prev.groupSelect,
          options: groupItems,
          selected: groupItems.map(item => item.key),
        },
      }));
    })();
  }, []);

  // const getCountryData = async () => {
  //   const { data } = await axios({
  //     method: 'POST',
  //     url: 'http://sandbox-app.cdp.asia/hub/toolkit/v2.0/suggestion/country-items?portalId=33167&languageCode=en&_user_id=1600085179&_owner_id=1600085328',
  //     headers: {
  //       token: '5474r2x214z2a46403e4y4p5r454c4s5w274l484x5m5',
  //     },
  //     data: {
  //       type: 'city-district',
  //       search: '',
  //       page: 1,
  //       limit: 20,
  //       sort: 'asc',
  //     },
  //   });

  //   const entries = get(data, 'data.entries', []);

  //   const items = entries.map(country => ({
  //     key: `country-${country.countryId}`,
  //     title: country.countryName,
  //     ...country,
  //     children: country.provinces?.map(({ provinceId, provinceName, cities, ...province }) => ({
  //       key: `province-${provinceId}`,
  //       title: provinceName,
  //       children: cities?.map(({ cityId, cityName, ...city }) => ({
  //         key: `city-${cityId}`,
  //         title: cityName,
  //         ...city,
  //       })),
  //       ...province,
  //     })),
  //   }));

  //   setState(prev => ({ ...prev, items }));
  // };
  // useEffect(() => {
  //   getCountryData();
  // }, []);

  // NOTE: For debug only
  console.log('state', state);

  return (
    <MatchesAnySelect
      objectName="State/ Province"
      items={state.items}
      selectedItems={state.selectedItems}
      loading={state.loading}
      groupSelectProps={{
        name: 'Country',
        options: groupSelect.options,
        selected: groupSelect.selected,
        onApply(selected) {
          setState(prev => ({ ...prev, groupSelect: { ...prev.groupSelect, selected } }));
        },
      }}
      onChange={selectedItems => setState(prev => ({ ...prev, selectedItems }))}
      {...args}
    />
  );
};

const RenderMatchesAnyWithExtraValuesAndStatus = (args: MatchesAnySelectProps) => {
  const getError = (status: number) => {
    let error = '';

    switch (status) {
      case 3:
        error = 'This segment is archived';
        break;
      case 4:
        error = 'This segment does not exist';
        break;
      default:
        break;
    }

    return error;
  };

  const [state, setState] = useState<TState>({
    selectedItems: [],
    items: EXTRA_VALUE_SAMPLE_DATA.map(item => ({
      key: item.id,
      title: item.name,
      segmentSize: item.segmentSize,
      rowCount: item.rowCount,
      subTitle: item.id?.toString(),
      status: item.status,
      error: getError(item.status),
    })),
    searchValue: '',
  });

  const memoItems = useMemo(
    () =>
      recursiveSearchItems<MatchesAnyItem<TSegmentType>>({
        list: state.items || [],
        searchValue: state.searchValue || '',
        searchKeys: ['title', 'key'],
        childrenKey: 'children',
      }).results,
    [state.items, state.searchValue],
  );
  console.log('matchesAnyItems:: ', state.items);
  console.log('selectedItems:: ', state.selectedItems);

  return (
    <MatchesAnySelect<TSegmentType>
      items={memoItems}
      objectName="Is member of"
      selectedItems={state.selectedItems}
      /** Support for render extra values */
      renderExtraValues={[
        {
          key: 'rowCount',
          label: 'Rows',
        },
        {
          key: 'segmentSize',
          label: 'Members',
          render(item) {
            const parseNumber = parseInt(item.segmentSize);

            return isNaN(parseNumber) ? null : parseNumber.toLocaleString();
          },
        },
      ]}
      /** Support for custom item renders such as title, subtitle, status */
      customItemRenders={{
        title(item) {
          return (
            <Typography.Link href={item.key.toString()}>{item.title?.toString()}</Typography.Link>
          );
        },
      }}
      /** Support for handle search outside  */
      onChangeSearch={searchValue => {
        console.log({ searchValue });

        setState(prev => ({
          ...prev,
          searchValue,
        }));
      }}
      onChange={selectedItems => {
        console.log({ selectedItems });

        setState(prev => ({ ...prev, selectedItems }));
      }}
    />
  );
};

const RenderCustomEmptyUI = (args: MatchesAnySelectProps) => (
  <MatchesAnySelect<TSegmentType>
    items={[]}
    objectName="Is member of"
    selectedItems={[]}
    /** Support for render extra values */
    listEmptyProps={{
      icon: <LongerIcon />,
      description: (
        <>
          You don&lsquo;t have any value <br /> Enable Auto suggestion or click Extend Value to add
          value
        </>
      ),
    }}
    selectedListEmptyProps={{
      icon: <HelpIcon />,
      description: 'Give me some value',
    }}
  />
);

const RenderMatchesAnyWithSelectedTreeData = (args: MatchesAnySelectProps) => {
  const [state, setState] = useState<any>({
    selectedTreeData: [],
    selectedItems: [],
    items: [],
    originItems: [],
    loading: false,
    groupSelect: {
      options: [],
    },
  });
  const { groupSelect } = state;

  const getCountryData = async () => {
    const items = countryItems.map(item => ({
      key: item.key,
      title: item.title,
      children: item.children.map(child => ({
        key: child.key,
        title: child.title,
      })),
    }));

    setState(prev => ({
      ...prev,
      items,
      originItems: items,
      groupSelect: {
        ...prev.groupSelect,
        options: items.map(item => ({ label: item.title, key: item.key })),
        selected: items.map(item => item.key),
      },
    }));
  };

  useEffect(() => {
    getCountryData();
  }, []);

  const onApplyGroupSelect = (selected: string[]) => {
    const newItems = state.originItems.filter(item => selected.includes(item.key));

    setState(prev => ({
      ...prev,
      groupSelect: { ...prev.groupSelect, selected },
      items: newItems,
    }));
  };

  const onChangeMatchesAnySelect = (
    selectedItems: MatchesAnyItem<TSegmentType>[],
    selectedTreeData?: MatchesAnyItem<TSegmentType>[],
  ) => {
    setState(prev => ({ ...prev, selectedItems, selectedTreeData }));
  };

  return (
    <MatchesAnySelect<TSegmentType>
      objectName="State/ Province"
      items={state.items}
      selectedItems={state.selectedItems}
      selectedTreeData={state.selectedTreeData}
      loading={state.loading}
      groupSelectProps={{
        name: 'Country',
        options: groupSelect.options,
        selected: groupSelect.selected,
        onApply: onApplyGroupSelect,
      }}
      onChange={onChangeMatchesAnySelect}
      {...args}
    />
  );
};

export const Default: Story = {
  render: args => <RenderMatchesAnySelectDefault {...args} />,
  args: {
    objectName: 'Province',
  },
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
// Libraries
import React, { useState } from 'react';
import { MATCHES_ANY_SAMPLE_DATA, MatchesAnySelect } from '@antscorp/antsomi-ui';

export const MatchesAnySelectTest = () => {
  const [state, setState] = useState<any>({
    selectedItems: [],
    items: MATCHES_ANY_SAMPLE_DATA,
  });

  return (
    <MatchesAnySelect
      items={state.items}
      selectedItems={state.selectedItems}
      onChange={selectedItems => setState(prev => ({ ...prev, selectedItems }))}
    />
  );
};

        `,
      },
    },
  },
};

export const MatchesAnySelectWithGroup: Story = {
  render: args => <RenderMatchesAnyWithGroupSelection {...args} />,
  args: {},
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderMatchesAnyWithGroupSelection = (args: MatchesAnySelectProps) => {
  const [state, setState] = useState<any>({
    selectedItems: [],
    items: [],
    loading: true,
    groupSelect: {
      options: [],
    },
  });
  const { groupSelect } = state;

  useEffect(() => {
    (async () => {
      const response = await fetch('https://restcountries.com/v3.1/all');
      const data = await response.json();

      const groupItems = data.splice(0, 10).map(item => ({
        key: item.cca2,
        label: item.name?.common,
      }));

      const items = await Promise.all(
        groupItems.map(async groupItem => {
          const response = await fetch(
            "https://secure.geonames.org/searchJSON?country=" + groupItem.key + "&featureClass=P&maxRows=10&username=nltruongvi1999",
          );

          const data = await response.json();

          return {
            key: groupItem.key,
            title: groupItem.label,
            children:
              data?.geonames?.map(item => ({
                key: item.geonameId,
                title: item.toponymName,
              })) || [],
          };
        }),
      );

      setState(prev => ({
        ...prev,
        loading: false,
        items: items.filter(Boolean),
        groupSelect: {
          ...prev.groupSelect,
          options: groupItems,
          selected: groupItems.map(item => item.key),
        },
      }));
    })();
  }, []);

  // NOTE: For debug only
  console.log('state', state);

  return (
    <MatchesAnySelect
      objectName="State/ Province"
      items={state.items}
      selectedItems={state.selectedItems}
      loading={state.loading}
      groupSelectProps={{
        name: 'Country',
        options: groupSelect.options,
        selected: groupSelect.selected,
        onApply(selected) {
          setState(prev => ({ ...prev, groupSelect: { ...prev.groupSelect, selected } }));
        },
      }}
      onChange={selectedItems => setState(prev => ({ ...prev, selectedItems }))}
      {...args}
    />
  );
};
        `,
      },
    },
  },
};

export const MatchesAnySelectWithExtraValuesAndStatus: Story = {
  render: args => <RenderMatchesAnyWithExtraValuesAndStatus {...args} />,
  args: {},
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
type TSegmentType = {
  segmentSize: string;
  rowCount: string;
  status: number;
};

type TState = {
  selectedItems: MatchesAnyItem<TSegmentType>[];
  items: MatchesAnyItem<TSegmentType>[];
  searchValue?: string;
};

export const MatchesAnyWithExtraValuesAndStatus = () => {
   const getError = (status: number) => {
    let error = '';

    switch (status) {
      case 3:
        error = 'This segment is archived';
        break;
      case 4:
        error = 'This segment does not exist';
        break;
      default:
        break;
    }

    return error;
  };

  const [state, setState] = useState<TState>({
    selectedItems: [],
    items: EXTRA_VALUE_SAMPLE_DATA.map(item => ({
      key: item.id,
      title: item.name,
      segmentSize: item.segmentSize,
      rowCount: item.rowCount,
      subTitle: item.id?.toString(),
      status: item.status,
      error: getError(item.status),
    })),
    searchValue: '',
  });

  const memoItems = useMemo(
    () =>
      recursiveSearchItems<MatchesAnyItem<TSegmentType>>({
        list: state.items || [],
        searchValue: state.searchValue || '',
        searchKeys: ['title', 'key'],
        childrenKey: 'children',
      }).results,
    [state.items, state.searchValue],
  );
  console.log('matchesAnyItems:: ', state.items);
  console.log('selectedItems:: ', state.selectedItems);

  return (
    <MatchesAnySelect<TSegmentType>
      items={memoItems}
      objectName="Is member of"
      selectedItems={state.selectedItems}
      /** Support for render extra values */
      renderExtraValues={[
        {
          key: 'rowCount',
          label: 'Rows',
        },
        {
          key: 'segmentSize',
          label: 'Members',
          render(item) {
            const parseNumber = parseInt(item.segmentSize);

            return isNaN(parseNumber) ? null : parseNumber.toLocaleString();
          },
        },
      ]}
      /** Support for custom item renders such as title, subtitle, status */
      customItemRenders={{
        title(item) {
          return (
            <Typography.Link href={item.key.toString()}>{item.title?.toString()}</Typography.Link>
          );
        },
      }}
      /** Support for handle search outside  */
      onChangeSearch={searchValue => {
        console.log({ searchValue });

        setState(prev => ({
          ...prev,
          searchValue,
        }));
      }}
      onChange={selectedItems => {
        console.log({ selectedItems });

        setState(prev => ({ ...prev, selectedItems }));
      }}
    />
  );
        `,
      },
    },
  },
};

export const CustomEmptyUI: Story = {
  render: args => <RenderCustomEmptyUI {...args} />,
  args: {},
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const CustomEmptyUI = () => (
  <MatchesAnySelect<TSegmentType>
    items={[]}
    objectName="Is member of"
    selectedItems={[]}
    /** Support for render extra values */
    listEmptyProps={{
      icon: <LongerIcon />,
      description: (
        <>
          You don't have any value <br /> Enable Auto suggestion or click Extend Value to add value
        </>
      ),
    }}
    selectedListEmptyProps={{
      icon: <HelpIcon />,
      description: 'Give me some value',
    }}
  />
);
        `,
      },
    },
  },
};

export const MatchesAnyWithSelectedTreeData: Story = {
  render: args => <RenderMatchesAnyWithSelectedTreeData {...args} />,
  args: {},
  name: 'MatchesAnyWithSelectedTreeData',
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderMatchesAnyWithSelectedTreeData = (args: MatchesAnySelectProps) => {
  const [state, setState] = useState<any>({
    selectedTreeData: [],
    selectedItems: [],
    items: [],
    originItems: [],
    loading: false,
    groupSelect: {
      options: [],
    },
  });
  const { groupSelect } = state;

  const getCountryData = async () => {
    const items = countryItems.map(item => ({
      key: item.key,
      title: item.title,
      children: item.children.map(child => ({
        key: child.key,
        title: child.title,
      })),
    }));

    setState(prev => ({
      ...prev,
      items,
      originItems: items,
      groupSelect: {
        ...prev.groupSelect,
        options: items.map(item => ({ label: item.title, key: item.key })),
        selected: items.map(item => item.key),
      },
    }));
  };

  useEffect(() => {
    getCountryData();
  }, []);

  const onApplyGroupSelect = (selected: string[]) => {
    const newItems = state.originItems.filter(item => selected.includes(item.key));

    setState(prev => ({
      ...prev,
      groupSelect: { ...prev.groupSelect, selected },
      items: newItems,
    }));
  };

  const onChangeMatchesAnySelect = (
    selectedItems: MatchesAnyItem<TSegmentType>[],
    selectedTreeData?: MatchesAnyItem<TSegmentType>[],
  ) => {
    setState(prev => ({ ...prev, selectedItems, selectedTreeData }));
  };

  return (
    <MatchesAnySelect<TSegmentType>
      objectName="State/ Province"
      items={state.items}
      selectedItems={state.selectedItems}
      selectedTreeData={state.selectedTreeData}
      loading={state.loading}
      groupSelectProps={{
        name: 'Country',
        options: groupSelect.options,
        selected: groupSelect.selected,
        onApply: onApplyGroupSelect,
      }}
      onChange={onChangeMatchesAnySelect}
      {...args}
    />
  );
};
        `,
      },
      description: {
        component:
          'Support passing selectedTreeData into the component to support full display of selected items in case the items have not finished loading',
      },
    },
  },
};

export const MatchesAnyWithError: Story = {
  render: args => <RenderMatchesAnyWithSelectedTreeData {...args} />,
  args: {
    error: 'Field is required',
  },
};
