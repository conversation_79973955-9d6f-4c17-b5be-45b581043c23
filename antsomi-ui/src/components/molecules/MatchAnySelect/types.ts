// Libraries
import { TreeDataNode } from 'antd';
import { ReactNode } from 'react';

// Types
import type { EmptyDataProps, SelectProps } from '@antscorp/antsomi-ui/es/components/molecules';
import type { FlexProps, PopoverProps } from '@antscorp/antsomi-ui/es/components/atoms';
import { PopoverSelectProps } from '../SearchPopover/types';

export type MatchesAnyItem<TItem = any> = TreeDataNode & {
  /**  Addition title of the item. */
  subTitle?: string;
  /** Indicates whether the item is extended */
  isExtendValue?: boolean;
  /** An array of children items. */
  children?: MatchesAnyItem<TItem>[];
  /** An error message */
  error?: string;
} & TItem;

export type GroupSelectProps = PopoverSelectProps & {
  /** Name of the group select to be displayed */
  name?: string;
};

export type RenderExtraValue<TItem = any> = {
  key: keyof MatchesAnyItem<TItem>;
  label: string;
  render?: (item: MatchesAnyItem<TItem>) => ReactNode | ReactNode;
};

export type CustomItemRenders<TItem> = {
  title?: (item: MatchesAnyItem<TItem>) => ReactNode;
  subTitle?: (item: MatchesAnyItem<TItem>) => ReactNode;
  error?: (item: MatchesAnyItem<TItem>) => ReactNode;
};

export interface MatchesAnySelectProps<TItem = any> extends Omit<SelectProps, 'onChange'> {
  /** Maximum item can select from items list */
  maxSelectedItem?: number;
  /** Max character of each item selected
   * @default 200
   */
  maxLengthEachItem?: number;
  /** The name of object to be displayed in Popover */
  objectName?: string;
  /** Indicates whether show the extend value button */
  showExtendValue?: boolean;
  /** An array of items. */
  items?: MatchesAnyItem<TItem>[];
  /** An array of selected items. */
  selectedItems?: MatchesAnyItem<TItem>[];
  /** Selected Tree Data serve for case items not load complete for showing fully displayed selected items */
  selectedTreeData?: MatchesAnyItem<TItem>[];
  /** The properties of the group select */
  groupSelectProps?: GroupSelectProps;
  /** An array of render extra values */
  renderExtraValues?: RenderExtraValue<TItem>[];
  /** Custom item renders */
  customItemRenders?: CustomItemRenders<TItem>;
  /** Popover Props */
  popoverProps?: Partial<PopoverProps>;
  /** Empty UI for list items */
  listEmptyProps?: EmptyDataProps;
  /** Empty UI for selected list items */
  selectedListEmptyProps?: EmptyDataProps;
  /** Search value */
  searchValue?: string;
  /** Error message */
  error?: string;
  /** Callback function that is called when the selected items change. */
  onChange?: (
    selectedItems: MatchesAnyItem<TItem>[],
    selectedTreeData?: MatchesAnyItem<TItem>[],
  ) => void;
  /** Callback function that is called when the search value changes */
  onChangeSearch?: (searchValue: string) => void;
  /** Callback function that is called when the user scroll */
  onLoadMore?: () => void;
}

export interface MatchesAnyProps<TItem = any>
  extends Omit<FlexProps, 'children'>,
    Pick<
      MatchesAnySelectProps<TItem>,
      | 'objectName'
      | 'loading'
      | 'showExtendValue'
      | 'items'
      | 'selectedItems'
      | 'onLoadMore'
      | 'groupSelectProps'
      | 'onChangeSearch'
      | 'renderExtraValues'
      | 'customItemRenders'
      | 'listEmptyProps'
      | 'selectedListEmptyProps'
      | 'selectedTreeData'
      | 'searchValue'
      | 'maxSelectedItem'
      | 'maxLengthEachItem'
    > {
  /** Indicates handle search out of component */
  isExternalSearch?: boolean;
  /**
   * Callback function that is called when the apply action is triggered.
   *
   * @param selectedItems - The array of selected items.
   */
  onApply?: (
    selectedItems: MatchesAnyItem<TItem>[],
    selectedTreeData?: MatchesAnyItem<TItem>[],
  ) => void;
  /** Callback function that is called when the cancel action is triggered. */
  onCancel?: () => void;
}
