// Libraries
import { uniq } from 'lodash';
import React, { useState } from 'react';
import styled from 'styled-components';

// Components
import { ApplyPopupContent, Form } from '@antscorp/antsomi-ui/es/components/molecules';
import { Input, Popover, type PopoverProps } from '@antscorp/antsomi-ui/es/components/atoms';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Utils
import { splitTextByNewline } from '@antscorp/antsomi-ui/es/utils';

const { borderRadius, blue1 } = globalToken || {};
const { t } = i18nInstance;

interface ExtendValuePopupProps extends PopoverProps {
  onApply?: (extendValues: string[]) => void;
}

// Styled
const TextArea = styled(props => <Input.TextArea {...props} />)`
  &.antsomi-input-borderless {
    border: 1px solid ${blue1} !important;
    border-radius: ${borderRadius}px;
  }
`;

type TValueType = {
  extendValues: string;
};

export const ExtendValuePopup: React.FC<ExtendValuePopupProps> = props => {
  // Props
  const { children, onApply = () => {}, ...restProps } = props;

  // State
  const [state, setState] = useState({
    isOpenPopover: false,
  });

  // Variables
  const { isOpenPopover } = state;

  // Hooks
  const [form] = Form.useForm();

  // Form
  const extendValues = Form.useWatch('extendValues', form);

  // Handlers
  const onClosePopover = () => {
    setState(prev => ({ ...prev, isOpenPopover: false }));
    form.resetFields();
  };

  const onFinishSubmit = (values: TValueType) => {
    const extendValues = uniq(splitTextByNewline(values.extendValues).filter(Boolean));

    onApply(extendValues);
    onClosePopover();
  };

  return (
    <Popover
      open={isOpenPopover}
      overlayInnerStyle={{
        padding: 0,
        width: 500,
      }}
      trigger={['click']}
      arrow={false}
      placement="topRight"
      onOpenChange={() => setState(prev => ({ ...prev, isOpenPopover: !isOpenPopover }))}
      {...restProps}
      content={
        <ApplyPopupContent
          title={t(translations.extendValue.title).toString()}
          applyButtonProps={{
            disabled: !extendValues?.trim(),
          }}
          onCancel={onClosePopover}
          onApply={() => form.submit()}
        >
          <Form form={form} onFinish={onFinishSubmit}>
            <Form.Item noStyle name="extendValues">
              <TextArea
                autoSize={false}
                bordered={false}
                rows={9}
                placeholder={t(translations.extendValue.placeholder).toString()}
              />
            </Form.Item>
          </Form>
        </ApplyPopupContent>
      }
    >
      {children}
    </Popover>
  );
};
