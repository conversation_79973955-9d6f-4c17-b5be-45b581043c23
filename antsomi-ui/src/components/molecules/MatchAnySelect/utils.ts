// Libraries
import deepmerge from 'deepmerge';
import { isEmpty, unionBy } from 'lodash';

// Types
import { MatchesAnyItem } from './types';

// Utils
import { flatTree } from '@antscorp/antsomi-ui/es/utils';

/**
 * Creates a custom merge function for tree data structures.
 *
 * @param {string} key - The property name used to identify unique items in the tree.
 * @returns {Function} A function that merges two arrays of MatchesAnyItem objects.
 *
 * @description
 * This function returns a merge strategy for deepmerge that:
 * 1. Merges items from the target and source arrays based on the specified key.
 * 2. Recursively merges nested children using the same strategy.
 * 3. Adds new items from the source that don't exist in the target.
 *
 * @example
 * const mergeStrategy = customMergeTreeData('id');
 * const mergedData = deepmerge(targetArray, sourceArray, { arrayMerge: mergeStrategy });
 */
const customMergeTreeData =
  (key: string) => (target: MatchesAnyItem<any>[], source: MatchesAnyItem<any>[]) => {
    const merged = target.map(item => {
      const found = source.find(s => s[key] === item[key]);
      if (found) {
        return deepmerge(found, item, { arrayMerge: customMergeTreeData(key) });
      }
      return item;
    });

    const newItems = source.filter(s => !target.some(t => t[key] === s[key]));

    return [...merged, ...newItems];
  };

export const getSelectedTreeData = (
  items: MatchesAnyItem<any>[],
  selectedItems: MatchesAnyItem<any>[],
  selectedTreeData: MatchesAnyItem<any>[] = [],
) => {
  const notExtendValueSelectedItems = selectedItems?.filter(item => !item.isExtendValue) || [];
  const extendValueSelectedItems = selectedItems?.filter(item => item.isExtendValue) || [];

  const serializeTreeData = (list: MatchesAnyItem<any>[]) =>
    list
      .filter(item => {
        /**
         * Checks if the given item or any of its descendants are selected.
         *
         * If the item has children, it flattens the children tree structure and checks if any node is selected.
         * If the item does not have children, it directly checks if the item is in the selected items list.
         *
         */
        const isSelected = item.children
          ? flatTree<MatchesAnyItem<any>>(item.children, 'children').some(item =>
              notExtendValueSelectedItems?.some(selectedItem => selectedItem.key === item.key),
            )
          : notExtendValueSelectedItems?.some(selectedItem => selectedItem.key === item.key);

        return isSelected;
      })
      ?.map(item => ({
        ...item,
        ...(item.children && {
          children: serializeTreeData(item.children),
        }),
      })) || [];

  const mergeItems = deepmerge(selectedTreeData || [], items || [], {
    arrayMerge: customMergeTreeData('key'),
  });

  // If all of items not have children then filter item is not found in list but has selected for display
  const notFoundItems = !mergeItems.some(item => !isEmpty(item.children))
    ? notExtendValueSelectedItems?.filter(item => !items?.some(i => i.key === item.key))
    : [];

  const treeData = serializeTreeData(mergeItems).concat([
    ...notFoundItems,
    ...extendValueSelectedItems,
  ]);

  return unionBy(treeData, 'key');
};
