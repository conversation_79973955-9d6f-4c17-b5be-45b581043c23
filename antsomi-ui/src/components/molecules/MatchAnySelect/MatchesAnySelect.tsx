/* eslint-disable react/no-danger */
/* eslint-disable react/function-component-definition */
/* eslint-disable react/destructuring-assignment */
// Libraries
import { ConfigProvider, InputRef } from 'antd';
import clsx from 'clsx';
import { cloneDeep, isEmpty, uniqBy } from 'lodash';
import React, { useCallback, useRef, useState } from 'react';

// Components
import {
  Button,
  Flex,
  Icon,
  Input,
  Popover,
  Spin,
  Tooltip,
  Typography,
} from '@antscorp/antsomi-ui/es/components/atoms';
import { EmptyData, PopoverSelect, Select } from '@antscorp/antsomi-ui/es/components/molecules';
import { ExtendValuePopup } from './components/ExtendValuePopup';

// Styled
import {
  ActionButton,
  ExtraValueLabel,
  GroupSelectButton,
  MatchesAnyWrapper,
  StyledTree,
  TextButton,
} from './styled';

// Locales
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { MATCHES_ANY_THEME } from './constants';

// Hooks
import {
  useDeepCompareEffect,
  useDeepCompareMemo,
  useIntersectionObserver,
} from '@antscorp/antsomi-ui/es/hooks';

// Utils
import {
  flatTree,
  recursiveFindParents,
  recursiveSearchItems,
} from '@antscorp/antsomi-ui/es/utils';
import { getSelectedTreeData } from './utils';

// Types
import { MatchesAnyItem, MatchesAnyProps, MatchesAnySelectProps } from './types';

// Icons
import { DataIcon, ErrorIcon, LongerIcon, WarningIcon } from '../../icons';
import { translate, translations as translationsLocale } from '@antscorp/antsomi-locales';

type TState = {
  isOpenPopover: boolean;
  searchValue: string;
};

type TMatchesAnyState = {
  searchValue: string;
  selectedItems?: MatchesAnyItem<any>[];
  selectedTreeData?: MatchesAnyItem<any>[];
  isShowLoadMoreEl?: boolean;
  expandedKeys?: React.Key[];
  selectedExpandedKeys?: React.Key[];
};

const initialState: TState = {
  isOpenPopover: false,
  searchValue: '',
};
const matchesAnyInitialState: TMatchesAnyState = {
  searchValue: '',
  selectedItems: [],
  selectedTreeData: [],
  isShowLoadMoreEl: false,
  expandedKeys: [],
  selectedExpandedKeys: [],
};
const { t } = i18nInstance;
const { Text } = Typography;

export function MatchesAny<TItem = any>(props: MatchesAnyProps<TItem>) {
  // Props
  const {
    maxSelectedItem,
    maxLengthEachItem,
    objectName,
    loading = false,
    showExtendValue = true,
    selectedItems: selectedItemsProp,
    items,
    groupSelectProps,
    renderExtraValues,
    customItemRenders,
    listEmptyProps,
    selectedListEmptyProps,
    selectedTreeData: selectedTreeDataProp,
    searchValue = '',
    isExternalSearch,
    onApply = () => {},
    onCancel = () => {},
    onLoadMore = () => {},
    onChangeSearch,
    ...restOfProps
  } = props;

  // State
  const [state, setState] = useState<TMatchesAnyState>(matchesAnyInitialState);
  const [maxItemsError, setMaxItemsError] = useState(false);
  const [itemOverMaxLength, setItemOverMaxLength] = useState(false);
  const [isApply, setIsApply] = useState(false);

  // Variables
  const { selectedItems, selectedTreeData, isShowLoadMoreEl, expandedKeys, selectedExpandedKeys } =
    state;

  const {
    icon: listEmptyIcon = <LongerIcon />,
    description: listEmptyDescription = (
      <div dangerouslySetInnerHTML={{ __html: t(translations.matchesAnySelect.noData) }} />
    ),
    ...restListEmptyProps
  } = listEmptyProps || {};
  const {
    description: selectedListEmptyDescription = t(
      translations.global.selectItemsFromList,
    ).toString(),
    icon: selectedListEmptyIcon = <DataIcon color={globalToken?.bw5} size={48} />,
    ...restSelectedListEmptyProps
  } = selectedListEmptyProps || {};

  useDeepCompareEffect(() => {
    const isOverMaxSelected =
      maxSelectedItem && selectedItems && selectedItems.length > maxSelectedItem;
    const isSomeTextTooLong = maxLengthEachItem
      ? selectedItems && selectedItems.some(item => item.key?.toString().length > maxLengthEachItem)
      : false;

    setMaxItemsError(!!isOverMaxSelected);

    setItemOverMaxLength(!!isSomeTextTooLong);

    if (isApply && !isOverMaxSelected && !isSomeTextTooLong) setIsApply(false);
  }, [selectedItems, maxSelectedItem, maxLengthEachItem, isApply]);

  // Refs
  const { ref: loadMoreRef } = useIntersectionObserver({
    threshold: 0,
    initialIsIntersecting: false,
    onChange(isIntersecting) {
      // Only load more items if the search input is empty and not in external search mode.
      // This prevents loading more items when the user is searching for something specific.
      const isNoNeedToLoadMore = !!searchValue && !isExternalSearch;

      if (isIntersecting && !isNoNeedToLoadMore) {
        // Load more items when the user reaches the end of the list.
        onLoadMore();
      }
    },
  });
  const searchInputRef = useRef<InputRef>(null);

  // Effects
  /**
   * Updates the `selectedItems` state when the `selectedItems` prop changes.
   */
  useDeepCompareEffect(() => {
    setState(prev => ({
      ...prev,
      selectedItems: selectedItemsProp,
    }));
  }, [selectedItemsProp]);

  useDeepCompareEffect(() => {
    searchInputRef.current?.focus();

    // Delay for show load more el
    if (!isEmpty(items)) {
      setTimeout(() => {
        setState(prev => ({ ...prev, isShowLoadMoreEl: true }));
      }, 500);

      setState(prev => ({ ...prev, expandedKeys: items?.map(item => item.key) }));
    }
  }, [items]);

  useDeepCompareEffect(() => {
    // If `selectedTreeData` prop is not empty, update the `selectedTreeData` state directly.
    if (!isEmpty(selectedTreeDataProp)) {
      setState(prev => ({
        ...prev,
        selectedTreeData: selectedTreeDataProp,
        selectedExpandedKeys: flatTree(selectedTreeDataProp, 'children')
          ?.filter(item => !!item.children)
          ?.map(item => item.key),
      }));
    }
    // If `selectedTreeData` prop is empty and `selectedItems` prop is not empty,
    // calculate the `selectedTreeData` from the `selectedItems` prop and update the state.
    else if (!isEmpty(selectedItemsProp)) {
      const newSelectedTreeData = getSelectedTreeData(items || [], selectedItemsProp || []);

      setState(prev => ({
        ...prev,
        selectedTreeData: newSelectedTreeData,
        selectedExpandedKeys: flatTree<MatchesAnyItem>(newSelectedTreeData, 'children')
          ?.filter(item => !!item.children)
          ?.map(item => item.key),
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTreeDataProp, selectedItemsProp]);

  // Handlers
  const onSelectItem = (item: MatchesAnyItem<TItem>) => {
    let newSelectedItems: MatchesAnyItem<TItem>[] = cloneDeep(selectedItems || []);
    let newSelectedExpandKeys = cloneDeep(selectedExpandedKeys || []);

    if (item.children) {
      const flattenChild = flatTree<MatchesAnyItem<TItem>>(item.children, 'children');

      newSelectedItems = newSelectedItems.concat(flattenChild.filter(item => !item.children));
    } else {
      newSelectedItems = newSelectedItems.concat(item);
    }

    newSelectedItems = uniqBy(newSelectedItems, 'key');

    const newSelectedTreeData = getSelectedTreeData(
      items || [],
      newSelectedItems || [],
      selectedTreeData || [],
    );

    // Handle expand keys
    const matchesParents = recursiveFindParents(items || [], item, 'key', 'children');
    newSelectedExpandKeys = newSelectedExpandKeys.concat(
      flatTree(matchesParents, 'children')
        ?.filter(item => !!item.children)
        .map(item => item.key) || [],
    );

    setState(prev => ({
      ...prev,
      selectedItems: newSelectedItems,
      selectedExpandedKeys: newSelectedExpandKeys,
      selectedTreeData: newSelectedTreeData,
    }));
  };

  /**
   * Removes an item and its descendants from the list of selected items.
   *
   * This function updates the state by removing the specified item and, if applicable,
   * any of its descendants from the list of selected items.
   *
   */
  const onRemoveItem = (item: MatchesAnyItem<TItem>) => {
    setState(prev => {
      const newSelectedItems = prev.selectedItems?.filter(selectedItem => {
        if (item.children) {
          const flattenChild = flatTree<MatchesAnyItem<TItem>>(item.children, 'children');

          return (
            selectedItem.key !== item.key &&
            !flattenChild?.some(item => item.key === selectedItem.key)
          );
        }

        return selectedItem.key !== item.key;
      });
      const newSelectedTreeData = getSelectedTreeData(
        items || [],
        newSelectedItems || [],
        selectedTreeData || [],
      );

      return {
        ...prev,
        selectedItems: newSelectedItems,
        selectedTreeData: newSelectedTreeData,
      };
    });
  };

  const onRemoveAll = () => {
    setState(prev => ({ ...prev, selectedItems: [], selectedTreeData: [] }));
  };

  const onClickApply = () => {
    if (maxItemsError || itemOverMaxLength) {
      setIsApply(true);
      return;
    }

    onApply(selectedItems || [], selectedTreeData || []);
  };

  const handleClickRemoveInvalid = () => {
    selectedItems?.forEach(item => {
      if (maxLengthEachItem && item.key?.toString().length > maxLengthEachItem) {
        onRemoveItem(item);
      }
    });
    // setItemOverMaxLength(false);
  };

  const renderItemError = useCallback(
    (item: MatchesAnyItem<TItem>) => {
      if (customItemRenders?.error) {
        return customItemRenders?.error(item);
      }

      return item.error ? (
        <Tooltip title={item.error}>
          <WarningIcon size={16} />
        </Tooltip>
      ) : null;
    },
    [customItemRenders],
  );

  const renderItemNodeTitle = useCallback(
    ({
      item,
      mode = 'unselect',
      onSelectItem,
      onRemoveItem,
    }: {
      item: MatchesAnyItem<TItem>;
      mode?: 'select' | 'unselect';
      onSelectItem?: (item: MatchesAnyItem<TItem>) => void;
      onRemoveItem?: (item: MatchesAnyItem<TItem>) => void;
    }) => {
      const { isExtendValue, title, subTitle } = item; // Destructure properties from item

      return (
        <>
          <div
            className={clsx('title-content', {
              'extra-value': !!renderExtraValues?.length,
            })}
          >
            <Flex vertical gap={5} className="left-content">
              <Text ellipsis={{ tooltip: `${item.title || ''}` }}>
                {customItemRenders?.title?.(item) || (title as any)}
              </Text>
              {subTitle && (
                <Text
                  className="sub-title"
                  ellipsis={{ tooltip: `${item.subTitle || ''}` }}
                  style={{ color: globalToken?.bw8 }}
                >
                  {customItemRenders?.subTitle?.(item) || (subTitle as any)}
                </Text>
              )}
            </Flex>

            {!!renderExtraValues?.length && (
              <Flex align="center" gap={10}>
                {renderExtraValues.map(extraValue => (
                  <div className="extra-value-content" key={`${extraValue.key as any}`}>
                    <Text ellipsis={{ tooltip: true }}>
                      {extraValue.render
                        ? typeof extraValue.render === 'function'
                          ? extraValue.render(item)
                          : extraValue.render
                        : item[extraValue.key as any]}
                    </Text>
                  </div>
                ))}
              </Flex>
            )}
          </div>

          <Flex align="center" gap={10}>
            {/* Render an error */}
            {mode === 'select' && renderItemError(item)}

            {mode === 'select' &&
              item.key?.toString().length &&
              maxLengthEachItem &&
              item.key?.toString().length > maxLengthEachItem && <ErrorIcon size={16} />}

            {/* Render an icon if the item is an extend value */}
            {isExtendValue && (
              <Icon type="icon-ants-empty-flag" size={20} color={globalToken?.colorPrimary} />
            )}

            {/* Action button for selecting or removing the item */}
            <ActionButton
              className="select-button"
              align="center"
              justify="center"
              onClick={() => {
                // Call onSelectItem function if provided
                if (onSelectItem) {
                  onSelectItem(item);
                }

                // Call onRemoveItem function if provided
                if (onRemoveItem) {
                  onRemoveItem(item);
                }
              }}
            >
              {/* Render an icon based on whether onSelectItem function is provided */}
              <Icon
                type={`icon-ants-${onSelectItem ? 'double-arrow-up' : 'remove-slim'}`}
                size={11}
              />
            </ActionButton>
          </Flex>
        </>
      );
    },
    [renderExtraValues, customItemRenders, maxLengthEachItem, renderItemError],
  );

  // Memos
  const { treeData } = useDeepCompareMemo(() => {
    const { results, matchedParents } = recursiveSearchItems<MatchesAnyItem<TItem>>({
      list: items || [],
      searchKeys: ['title', 'key'],
      searchValue: !isExternalSearch ? searchValue : '',
      childrenKey: 'children',
    });

    const serializeTreeData = (list: MatchesAnyItem<TItem>[]) =>
      list.map(item => {
        /**
         * Checks if the given item is selected.
         *
         * If the item has children, it flattens the children tree structure and checks if all leaf nodes are selected (Compare key or title if item is extend value).
         * If the item does not have children, it directly checks if the item is in the selected items list.
         *
         */
        const isSelected = item.children
          ? flatTree(item.children, 'children')
              .filter(item => !item.children)
              .every(item =>
                selectedItems?.some(
                  selectedItem =>
                    selectedItem.key === item.key ||
                    (selectedItem.isExtendValue && selectedItem.title === item.title),
                ),
              )
          : selectedItems?.some(
              selectedItem =>
                selectedItem.key === item.key ||
                (selectedItem.isExtendValue && selectedItem.title === item.title),
            );

        return {
          ...item,
          disabled: isSelected,
          ...(item.children && {
            children: serializeTreeData(item.children),
          }),
        };
      });

    // Handle add load more item for serve load more
    let treeData = serializeTreeData(results);

    // Filter the tree data based on the group selected items
    if (groupSelectProps && groupSelectProps.selected?.length) {
      treeData = treeData.filter(item => groupSelectProps.selected?.includes(item.key));
    }

    treeData = treeData.concat({
      key: 'load-more',
      title: '',
      isLoadMore: true,
      className: 'load-more-node',
      disabled: true,
    });

    // If search value is empty, then expand all items
    let newExpandedKeys: React.Key[] = [];

    if (!searchValue) {
      // Expand all items
      newExpandedKeys = items?.map(item => item.key) || [];
    } else {
      // If search value is not empty, then only expand the matched parents
      // of the search result
      if (!isExternalSearch) {
        // Expand the matched parents of the search result
        newExpandedKeys =
          flatTree(matchedParents, 'children')
            ?.filter(item => !!item.children)
            ?.map(item => item.key) || [];
      }

      // If search value is not empty and is external search, then expand all items
      // that have children
      if (isExternalSearch) {
        // Expand all items that have children
        newExpandedKeys =
          flatTree(items, 'children')
            ?.filter(item => !!item.children)
            .map(item => item.key) || [];
      }
    }

    setState(prev => ({
      ...prev,
      expandedKeys: newExpandedKeys,
    }));

    return { treeData, matchedParents };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items, searchValue, selectedItems, groupSelectProps, onChangeSearch]);

  const isDisableRemoveAll = useDeepCompareMemo(
    () => !selectedTreeData?.length,
    [selectedTreeData],
  );

  const isDisableSelectAll = useDeepCompareMemo(() => {
    const flattenTreeData = flatTree<MatchesAnyItem>(treeData, 'children');

    return !flattenTreeData?.some(item => !item.disabled);
  }, [treeData]);

  // Select tree key for force hard rerender tree when items not full (< 15 items) for handle load more
  // We use the length of items as key to force rerender tree when items length change
  // This is to fix the issue load more not called when items length is less than 15
  const selectTreeKey = useDeepCompareMemo(
    () => ((flatTree(items, 'children', 1)?.length || 0) < 15 ? `${items?.length || 0}` : ''),
    [items],
  );
  const isDisableApply = useDeepCompareMemo(() => isEmpty(selectedTreeData), [selectedTreeData]);

  const onSelectAll = () => {
    const flattenTreeData = flatTree<MatchesAnyItem>(treeData, 'children').filter(
      item => !item.children && !item.disabled,
    );
    const newSelectedExpandedKeys = flatTree<MatchesAnyItem>(treeData, 'children')
      .filter(item => !!item.children && !item.disabled)
      .map(item => item.key);

    const newSelectedItems = uniqBy([...(state.selectedItems || []), ...flattenTreeData], 'key');
    const newSelectedTreeData = getSelectedTreeData(
      items || [],
      newSelectedItems || [],
      selectedTreeData,
    );

    setState(prev => ({
      ...prev,
      selectedItems: newSelectedItems,
      selectedExpandedKeys: newSelectedExpandedKeys,
      selectedTreeData: newSelectedTreeData,
    }));
  };

  // Renders
  const renderExtraValueLabels = () => {
    if (renderExtraValues?.length) {
      return renderExtraValues.map((extraValue, index) => (
        <ExtraValueLabel key={(extraValue.key as any) || index}>
          <Text strong ellipsis={{ tooltip: true }}>
            {extraValue.label}
          </Text>
        </ExtraValueLabel>
      ));
    }

    return null;
  };

  const renderSelectList = () => (
    <div className="matches-any__section matches-any__section--left">
      <div className="matches-any__header">
        {!!groupSelectProps && (
          <PopoverSelect {...groupSelectProps}>
            <GroupSelectButton type="default" size="small" shape="round">
              <span className="group-name">{groupSelectProps?.name}:</span>
              {`${groupSelectProps?.selected?.length || 0} ${t(translations.global.selected)}`}
            </GroupSelectButton>
          </PopoverSelect>
        )}

        <div style={{ width: '100%' }}>
          <Input.CustomSearch
            ref={searchInputRef}
            autoFocus
            value={searchValue}
            placeholder={translate(translationsLocale._ACT_SEARCH)}
            onAfterChange={searchValue => {
              if (onChangeSearch) {
                onChangeSearch(searchValue);
              }
            }}
          />
        </div>
      </div>
      <div className="matches-any__body">
        <Flex align="center" justify="space-between">
          <Text strong>{`${objectName} (${
            flatTree(treeData, 'children').filter(item => !item.children).length - 1
          })`}</Text>

          <Flex align="center" gap={10}>
            {renderExtraValueLabels()}

            <TextButton disabled={isDisableSelectAll} onClick={onSelectAll}>
              {t(translations.global.selectAll).toString()}
            </TextButton>
          </Flex>
        </Flex>

        <Spin spinning={loading}>
          {/* Check tree data length larger than 1 (1 is load more) then show tree otherwise show empty data */}
          {treeData?.length > 1 ? (
            <StyledTree
              key={selectTreeKey}
              expandedKeys={expandedKeys}
              selectable={false}
              treeData={treeData}
              switcherIcon={props => (
                <Icon
                  type="icon-ants-caret-down"
                  style={{
                    transform: props.expanded ? 'rotate(0)' : 'rotate(-90deg)',
                    transition: 'transform 0.3s ease',
                  }}
                  color={globalToken?.bw8}
                />
              )}
              titleRender={(item: any) =>
                isShowLoadMoreEl && item.isLoadMore ? (
                  <div ref={loadMoreRef} />
                ) : (
                  renderItemNodeTitle({
                    item,
                    onSelectItem,
                  })
                )
              }
              height={396}
              onExpand={expandedKeys => setState(prev => ({ ...prev, expandedKeys }))}
            />
          ) : (
            /**
             * If search value is not empty and no listEmpty props show no results matches key word message empty in case data is empty
             */
            !loading &&
            (!!searchValue && !listEmptyProps ? (
              <EmptyData
                showIcon={false}
                description={t(translations.global.noResultsMatchesKeyWord).toString()}
              />
            ) : (
              <EmptyData
                icon={listEmptyIcon}
                showIcon={!!listEmptyIcon}
                description={listEmptyDescription}
                {...restListEmptyProps}
              />
            ))
          )}
        </Spin>
      </div>
    </div>
  );

  const renderSelectedList = () => (
    <div className="matches-any__section">
      <Flex className="matches-any__header" justify="space-between">
        <Text
          strong
        >{`${t(translations.global.selected)} (${selectedItems?.length || 0}${maxSelectedItem && maxSelectedItem > 0 ? `/${maxSelectedItem}` : ''})`}</Text>
        <TextButton disabled={isDisableRemoveAll} onClick={onRemoveAll}>
          {translate(translations._ACT_REMOVE_ALL)}
        </TextButton>
      </Flex>

      <div className="matches-any__body">
        {!selectedItems?.length && !loading ? (
          <EmptyData
            icon={selectedListEmptyIcon}
            description={selectedListEmptyDescription}
            {...restSelectedListEmptyProps}
          />
        ) : (
          <StyledTree
            defaultExpandAll
            expandedKeys={selectedExpandedKeys}
            selectable={false}
            treeData={selectedTreeData}
            titleRender={item =>
              renderItemNodeTitle({
                item: item as MatchesAnyItem<TItem>,
                mode: 'select',
                onRemoveItem,
              })
            }
            height={420}
            switcherIcon={props => (
              <Icon
                type="icon-ants-caret-down"
                style={{
                  transform: props.expanded ? 'rotate(0)' : 'rotate(-90deg)',
                  transition: 'transform 0.3s ease',
                }}
                color={globalToken?.bw8}
              />
            )}
            onExpand={selectedExpandedKeys => {
              setState(prev => ({ ...prev, selectedExpandedKeys }));
            }}
          />
        )}
      </div>
    </div>
  );

  // Handlers
  const onApplyExtendValue = (extendValues: string[]) => {
    // Filter out values that are already present in selectedItems
    const availableExtendValues = extendValues?.filter(
      value => !selectedItems?.some(item => item.title === value),
    );

    // If there are available values to add
    if (availableExtendValues) {
      // Create new items in the format required (MatchesAnyItem)
      const newSelectedItems: MatchesAnyItem<TItem>[] = [
        ...(selectedItems || []),
        ...(availableExtendValues.map(value => ({
          key: value,
          title: value,
          isExtendValue: true, // Flag to indicate that this item is an extend value
        })) as MatchesAnyItem<TItem>[]),
      ];
      const newSelectedTreeData = getSelectedTreeData(
        items || [],
        newSelectedItems || [],
        selectedTreeData || [],
      );

      setState(prev => ({
        ...prev,
        selectedItems: newSelectedItems,
        selectedTreeData: newSelectedTreeData,
      }));
    }
  };

  return (
    <ConfigProvider theme={MATCHES_ANY_THEME}>
      <MatchesAnyWrapper {...restOfProps} vertical justify="space-between">
        <Flex style={{ width: '100%', height: '100%' }}>
          {renderSelectList()}

          {renderSelectedList()}
        </Flex>

        <Flex className="matches-any__footer" align="center" justify="space-between">
          <Flex align="center" gap={10}>
            <Button type="primary" disabled={isDisableApply} onClick={onClickApply}>
              {translate(translationsLocale._ACT_APPLY)}
            </Button>
            <Button onClick={() => onCancel()}>{translate(translationsLocale._ACT_CANCEL)}</Button>
            {isApply && (maxItemsError || itemOverMaxLength) && (
              <>
                <Typography style={{ color: globalToken?.colorError }}>
                  {translate(translationsLocale._OPERATOR_MATCH_ANY_EX_VALUE_LIMIT)}
                </Typography>
                {itemOverMaxLength && (
                  <Button type="text" onClick={handleClickRemoveInvalid}>
                    {translate(translationsLocale._OPERATOR_MATCH_ANY_EX_VALUE_LIMIT_REMOVE_INV)}
                  </Button>
                )}
              </>
            )}
          </Flex>

          {showExtendValue && (
            <ExtendValuePopup
              getPopupContainer={trigger => trigger.parentElement || document.body}
              onApply={onApplyExtendValue}
            >
              <Button icon={<Icon type="icon-ants-empty-flag" />}>
                {t(translations.extendValue.title).toString()}
              </Button>
            </ExtendValuePopup>
          )}
        </Flex>
      </MatchesAnyWrapper>
    </ConfigProvider>
  );
}

export function MatchesAnySelect<TItem = any>(props: MatchesAnySelectProps<TItem>) {
  const {
    maxSelectedItem,
    maxLengthEachItem,
    placeholder = 'Select an item',
    dropdownStyle,
    objectName,
    selectedItems,
    items,
    loading,
    popupClassName,
    groupSelectProps,
    renderExtraValues,
    customItemRenders,
    popoverProps,
    listEmptyProps,
    selectedListEmptyProps,
    selectedTreeData,
    error,
    onChangeSearch,
    onChange = () => {},
    onLoadMore,
    ...restProps
  } = props;

  // State
  const [state, setState] = useState<TState>(initialState);

  // Variables
  const { isOpenPopover } = state;

  const value =
    Array.isArray(selectedItems) && selectedItems?.length
      ? `${selectedItems?.map(item => item.title)?.[0]}${
          selectedItems?.slice(1)?.length ? `, and +${selectedItems?.slice(1)?.length} more` : ''
        }` || []
      : undefined;

  // Effects
  useDeepCompareEffect(() => {
    setState(prev => ({ ...prev, searchValue: props.searchValue || '' }));
  }, [props.searchValue]);

  // Handlers
  const onApplyMatchesAny = (
    selectedItems: MatchesAnyItem<TItem>[],
    selectedTreeData?: MatchesAnyItem<TItem>[],
  ) => {
    onChange(selectedItems, selectedTreeData);
    setState(prev => ({ ...prev, isOpenPopover: false }));
  };

  return (
    <Popover
      open={isOpenPopover}
      arrow={false}
      placement="bottomLeft"
      content={
        <MatchesAny<TItem>
          maxSelectedItem={maxSelectedItem}
          maxLengthEachItem={maxLengthEachItem}
          isExternalSearch={!!onChangeSearch}
          searchValue={state.searchValue}
          className={popupClassName}
          items={items}
          selectedItems={selectedItems}
          loading={loading}
          objectName={objectName}
          groupSelectProps={groupSelectProps}
          renderExtraValues={renderExtraValues}
          customItemRenders={customItemRenders}
          listEmptyProps={listEmptyProps}
          selectedListEmptyProps={selectedListEmptyProps}
          selectedTreeData={selectedTreeData}
          onChangeSearch={searchValue => {
            setState(prev => ({ ...prev, searchValue }));
            if (onChangeSearch) onChangeSearch(searchValue);
          }}
          onApply={onApplyMatchesAny}
          onCancel={() => setState(prev => ({ ...prev, isOpenPopover: false }))}
          onLoadMore={onLoadMore}
        />
      }
      align={{
        overflow: {
          shiftY: 0,
          shiftX: 0,
        },
      }}
      overlayStyle={{ width: 700 }}
      overlayInnerStyle={{
        padding: 0,
      }}
      trigger={['click']}
      destroyTooltipOnHide
      onOpenChange={() => setState(prev => ({ ...prev, isOpenPopover: !isOpenPopover }))}
      zIndex={1400}
      {...popoverProps}
    >
      <Tooltip
        mouseEnterDelay={0.5}
        title={`${selectedItems ? selectedItems?.map(item => item.title).join(', ') : ''}`}
        overlayInnerStyle={{
          maxHeight: 300,
          overflow: 'auto',
        }}
      >
        <Select
          title=""
          value={value}
          placeholder={placeholder}
          popupMatchSelectWidth={700}
          dropdownStyle={{
            ...dropdownStyle,
            display: 'none',
            padding: 0,
          }}
          loading={loading}
          status={props.status || (error ? 'error' : undefined)}
          {...restProps}
        />
        {!!error && (
          <Text className="ant-form-item-explain-error" type="danger">
            {error}
          </Text>
        )}
      </Tooltip>
    </Popover>
  );
}
