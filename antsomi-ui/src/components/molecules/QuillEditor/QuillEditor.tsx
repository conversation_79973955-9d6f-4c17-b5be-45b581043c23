import React, { useEffect, useRef } from 'react';
import Quill from 'quill';
import { ImageHandler } from 'quill-upload';

// Hooks
import debounce from 'lodash/debounce';

// Components
import EditorToolbar, { modules, formats } from './EditorToolbar';

// Styles
import 'quill/dist/quill.snow.css';
import styled from 'styled-components';

Quill.register('modules/imageHandler', ImageHandler);

const Block: any = Quill.import('blots/block');
Block.tagName = 'DIV';
Quill.register(Block, true);

// const toolbarOptions = [
//   ['bold', 'italic', 'underline', 'strike'],
//   ['blockquote', 'code-block'],
//   ['link', 'image', 'video', 'formula'],
//   [{ header: 1 }, { header: 2 }],
//   [{ list: 'ordered' }, { list: 'bullet' }, { list: 'check' }],
//   [{ script: 'sub' }, { script: 'super' }],
//   [{ indent: '-1' }, { indent: '+1' }],
//   [{ direction: 'rtl' }],
//   [{ size: ['small', false, 'large', 'huge'] }],
//   [{ header: [1, 2, 3, 4, 5, 6, false] }],
//   [{ color: [] }, { background: [] }],
//   [{ font: [] }],
//   [{ align: [] }],
//   ['clean'],
// ];

const EditorWrapper = styled.div<{ borderColor: string }>`
  border: 1px solid ${props => props.borderColor} !important;

  .ql-editor.ql-blank::before {
    font-style: normal !important;
  }

  .ql-editor a {
    color: #06c !important;
  }
`;

type TEditorProps = {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  uploadService?: Function;
  height?: string | number;
  maxImgHeight?: string;
  borderColor?: string;
  isRoundCorner?: boolean;
};

export const QuillEditor = ({
  value,
  onChange,
  placeholder = '',
  uploadService,
  height = 'auto',
  maxImgHeight = '300px',
  borderColor = '#d4d4d4',
  isRoundCorner,
}: TEditorProps) => {
  const editor = useRef<Quill | null>(null);
  const rand = useRef<string>(String(Math.random()).replace(/\./g, ''));

  const processText = () => {
    editor.current?.root.querySelectorAll('li').forEach(li => {
      let max = 0;
      li.querySelectorAll('[style*="font-size"]').forEach(el => {
        const fontS = +(el as HTMLElement).style.fontSize.replace(/\D/g, '');
        max = fontS > max ? fontS : max;
      });

      li.style.fontSize = max ? `${max}px` : 'unset';
    });
  };

  const debounceQuickProcess = debounce(processText, 100);
  const debounceChange = debounce(onChange, 500);

  // Upload handler function
  const _onUpload = async (file, resolve) => {
    try {
      const result = await uploadService!({ files: [file], mode: 'file' });
      if (result?.data?.length) {
        resolve(result.data[0].url);
        editor.current!.root.querySelectorAll('img.quill-upload-progress').forEach(el => {
          el.classList.remove('quill-upload-progress');
          (el as HTMLImageElement).style.maxHeight = maxImgHeight;
        });
      }
      resolve('');
    } catch (error) {
      resolve('');
    }
  };

  useEffect(() => {
    if (!value && editor.current) {
      editor.current.setText('');
    }
  }, [value]);

  useEffect(() => {
    if (editor.current) return;

    editor.current = new Quill(`#editor-${rand.current}`, {
      theme: 'snow',
      modules: {
        ...modules(`toolbar-${rand.current}`),
        imageHandler: uploadService
          ? {
              imageClass: 'custom-image-class',
              upload: file =>
                new Promise(resolve => {
                  if (file.size > 10 * 1024 * 1024) {
                    resolve('');
                    return;
                  }
                  _onUpload(file, resolve);
                }),
            }
          : undefined,
      },
      formats,
      placeholder,
    });

    editor.current.root.innerHTML = value || '';

    editor.current.on('text-change', (delta, oldDelta, source) => {
      // if (source === 'api') {}
      if (source === 'user') {
        debounceQuickProcess();
        if (editor.current?.getText().trim().length === 0) {
          debounceChange('');
        } else {
          debounceChange(editor.current?.root.innerHTML || '');
        }
      }
    });
  }, []);

  return (
    <>
      <EditorToolbar
        id={`toolbar-${rand.current}`}
        isRoundCorner={isRoundCorner}
        borderColor={borderColor}
      />
      <EditorWrapper
        id={`editor-${rand.current}`}
        style={{ height, color: '#000000' }}
        borderColor={borderColor}
      />
    </>
  );
};
