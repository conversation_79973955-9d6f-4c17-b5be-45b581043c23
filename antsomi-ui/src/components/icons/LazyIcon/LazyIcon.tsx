import { lazy } from 'react';

export const LazyIcon = {
  AccountCircleIcon: lazy(() =>
    import('../AccountCircleIcon').then(m => ({ default: m.AccountCircleIcon })),
  ),
  AccountCircleOutlineIcon: lazy(() =>
    import('../AccountCircleOutlineIcon').then(m => ({ default: m.AccountCircleOutlineIcon })),
  ),
  AccountSharing30Icon: lazy(() =>
    import('../AccountSharing30Icon').then(m => ({ default: m.AccountSharing30Icon })),
  ),
  AccountSharingIcon: lazy(() =>
    import('../AccountSharingIcon').then(m => ({ default: m.AccountSharingIcon })),
  ),
  AccountTreeIcon: lazy(() =>
    import('../AccountTreeIcon').then(m => ({ default: m.AccountTreeIcon })),
  ),
  Accounts30Icon: lazy(() =>
    import('../Accounts30Icon').then(m => ({ default: m.Accounts30Icon })),
  ),
  AddChartIcon: lazy(() => import('../AddChartIcon').then(m => ({ default: m.AddChartIcon }))),
  AddFolderIcon: lazy(() => import('../AddFolderIcon').then(m => ({ default: m.AddFolderIcon }))),
  AddIcon: lazy(() => import('../AddIcon').then(m => ({ default: m.AddIcon }))),
  AddRadiusIcon: lazy(() => import('../AddRadiusIcon').then(m => ({ default: m.AddRadiusIcon }))),
  AddShoppingCartIcon: lazy(() =>
    import('../AddShoppingCartIcon').then(m => ({ default: m.AddShoppingCartIcon })),
  ),
  AdjustIcon: lazy(() => import('../AdjustIcon').then(m => ({ default: m.AdjustIcon }))),
  AlignCenterIcon: lazy(() =>
    import('../AlignCenterIcon').then(m => ({ default: m.AlignCenterIcon })),
  ),
  AlignJustifyIcon: lazy(() =>
    import('../AlignJustifyIcon').then(m => ({ default: m.AlignJustifyIcon })),
  ),
  AlignLeftIcon: lazy(() => import('../AlignLeftIcon').then(m => ({ default: m.AlignLeftIcon }))),
  ALignRightIcon: lazy(() =>
    import('../ALignRightIcon').then(m => ({ default: m.ALignRightIcon })),
  ),
  AllDevicesIcon: lazy(() =>
    import('../AllDevicesIcon').then(m => ({ default: m.AllDevicesIcon })),
  ),
  AllJourneyChannelsIcon: lazy(() =>
    import('../AllJourneyChannelsIcon').then(m => ({ default: m.AllJourneyChannelsIcon })),
  ),
  AnalyticModels30Icon: lazy(() =>
    import('../AnalyticModels30Icon').then(m => ({ default: m.AnalyticModels30Icon })),
  ),
  AnalyticModelsIcon: lazy(() =>
    import('../AnalyticModelsIcon').then(m => ({ default: m.AnalyticModelsIcon })),
  ),
  Analytics30Icon: lazy(() =>
    import('../Analytics30Icon').then(m => ({ default: m.Analytics30Icon })),
  ),
  AnalyticsIcon: lazy(() => import('../AnalyticsIcon').then(m => ({ default: m.AnalyticsIcon }))),
  AnlysisIcon: lazy(() => import('../AnlysisIcon').then(m => ({ default: m.AnlysisIcon }))),
  AnlyticModelsIcon: lazy(() =>
    import('../AnlyticModelsIcon').then(m => ({ default: m.AnlyticModelsIcon })),
  ),
  ArrowDropDownIcon: lazy(() =>
    import('../ArrowDropDownIcon').then(m => ({ default: m.ArrowDropDownIcon })),
  ),
  ArrowGrowIcon: lazy(() => import('../ArrowGrowIcon').then(m => ({ default: m.ArrowGrowIcon }))),
  ArrowLineIcon: lazy(() => import('../ArrowLineIcon').then(m => ({ default: m.ArrowLineIcon }))),
  ArticleIcon: lazy(() => import('../ArticleIcon').then(m => ({ default: m.ArticleIcon }))),
  AssignmentIndIcon: lazy(() =>
    import('../AssignmentIndIcon').then(m => ({ default: m.AssignmentIndIcon })),
  ),
  AttachmentIcon: lazy(() =>
    import('../AttachmentIcon').then(m => ({ default: m.AttachmentIcon })),
  ),
  AudioRecordIcon: lazy(() =>
    import('../AudioRecordIcon').then(m => ({ default: m.AudioRecordIcon })),
  ),
  BatchStreaming30Icon: lazy(() =>
    import('../BatchStreaming30Icon').then(m => ({ default: m.BatchStreaming30Icon })),
  ),
  BatchStreamingIcon: lazy(() =>
    import('../BatchStreamingIcon').then(m => ({ default: m.BatchStreamingIcon })),
  ),
  BinaryIcon: lazy(() => import('../BinaryIcon').then(m => ({ default: m.BinaryIcon }))),
  BlockIcon: lazy(() => import('../BlockIcon').then(m => ({ default: m.BlockIcon }))),
  BudLightIcon: lazy(() => import('../BudLightIcon').then(m => ({ default: m.BudLightIcon }))),
  BugIcon: lazy(() => import('../BugIcon').then(m => ({ default: m.BugIcon }))),
  BusinessObjects30Icon: lazy(() =>
    import('../BusinessObjects30Icon').then(m => ({ default: m.BusinessObjects30Icon })),
  ),
  BussinessObjectsIcon: lazy(() =>
    import('../BussinessObjectsIcon').then(m => ({ default: m.BussinessObjectsIcon })),
  ),
  ByDayIcon: lazy(() => import('../ByDayIcon').then(m => ({ default: m.ByDayIcon }))),
  ByMonthIcon: lazy(() => import('../ByMonthIcon').then(m => ({ default: m.ByMonthIcon }))),
  ByScheduleIcon: lazy(() =>
    import('../ByScheduleIcon').then(m => ({ default: m.ByScheduleIcon })),
  ),
  ByThreeDayIcon: lazy(() =>
    import('../ByThreeDayIcon').then(m => ({ default: m.ByThreeDayIcon })),
  ),
  ByWeekIcon: lazy(() => import('../ByWeekIcon').then(m => ({ default: m.ByWeekIcon }))),
  CalendarIcon: lazy(() => import('../CalendarIcon').then(m => ({ default: m.CalendarIcon }))),
  CallIcon: lazy(() => import('../CallIcon').then(m => ({ default: m.CallIcon }))),
  CameraIcon: lazy(() => import('../CameraIcon').then(m => ({ default: m.CameraIcon }))),
  CampaignIcon: lazy(() => import('../CampaignIcon').then(m => ({ default: m.CampaignIcon }))),
  CampaignMenuIcon: lazy(() =>
    import('../CampaignMenuIcon').then(m => ({ default: m.CampaignMenuIcon })),
  ),
  CaptureIcon: lazy(() => import('../CaptureIcon').then(m => ({ default: m.CaptureIcon }))),
  CatalogIcon: lazy(() => import('../CatalogIcon').then(m => ({ default: m.CatalogIcon }))),
  CategoryIcon: lazy(() => import('../CategoryIcon').then(m => ({ default: m.CategoryIcon }))),
  ChartRealTimeIcon: lazy(() =>
    import('../ChartRealTimeIcon').then(m => ({ default: m.ChartRealTimeIcon })),
  ),
  ChartReviewIcon: lazy(() =>
    import('../ChartReviewIcon').then(m => ({ default: m.ChartReviewIcon })),
  ),
  ChartTypeIcon: lazy(() => import('../ChartTypeIcon').then(m => ({ default: m.ChartTypeIcon }))),
  ChatBubbleOutlineIcon: lazy(() =>
    import('../ChatBubbleOutlineIcon').then(m => ({ default: m.ChatBubbleOutlineIcon })),
  ),
  ChatConversationCommentAddCommentIcon: lazy(() =>
    import('../ChatConversationCommentAddCommentIcon').then(m => ({
      default: m.ChatConversationCommentAddCommentIcon,
    })),
  ),
  ChatMentionIcon: lazy(() =>
    import('../ChatMentionIcon').then(m => ({ default: m.ChatMentionIcon })),
  ),
  ChatUnreadIcon: lazy(() =>
    import('../ChatUnreadIcon').then(m => ({ default: m.ChatUnreadIcon })),
  ),
  CheckSlimIcon: lazy(() => import('../CheckSlimIcon').then(m => ({ default: m.CheckSlimIcon }))),
  CheckboxChecked5RadiusIcon: lazy(() =>
    import('../CheckboxChecked5RadiusIcon').then(m => ({ default: m.CheckboxChecked5RadiusIcon })),
  ),
  CheckboxCheckedIcon: lazy(() =>
    import('../CheckboxCheckedIcon').then(m => ({ default: m.CheckboxCheckedIcon })),
  ),
  CheckboxIndeterminate5RadiusIcon: lazy(() =>
    import('../CheckboxIndeterminate5RadiusIcon').then(m => ({
      default: m.CheckboxIndeterminate5RadiusIcon,
    })),
  ),
  CheckboxIndeterminateIcon: lazy(() =>
    import('../CheckboxIndeterminateIcon').then(m => ({ default: m.CheckboxIndeterminateIcon })),
  ),
  CheckboxUnchecked5RadiusIcon: lazy(() =>
    import('../CheckboxUnchecked5RadiusIcon').then(m => ({
      default: m.CheckboxUnchecked5RadiusIcon,
    })),
  ),
  CheckboxUncheckedIcon: lazy(() =>
    import('../CheckboxUncheckedIcon').then(m => ({ default: m.CheckboxUncheckedIcon })),
  ),
  ChecklistRtlIcon: lazy(() =>
    import('../ChecklistRtlIcon').then(m => ({ default: m.ChecklistRtlIcon })),
  ),
  ChevronRightIcon: lazy(() =>
    import('../ChevronRightIcon').then(m => ({ default: m.ChevronRightIcon })),
  ),
  CircleAddElementIcon: lazy(() =>
    import('../CircleAddElementIcon').then(m => ({ default: m.CircleAddElementIcon })),
  ),
  CircleInfoIcon: lazy(() =>
    import('../CircleInfoIcon').then(m => ({ default: m.CircleInfoIcon })),
  ),
  CircleNotificationsIcon: lazy(() =>
    import('../CircleNotificationsIcon').then(m => ({ default: m.CircleNotificationsIcon })),
  ),
  CloseIcon: lazy(() => import('../CloseIcon').then(m => ({ default: m.CloseIcon }))),
  CodeIcon: lazy(() => import('../CodeIcon').then(m => ({ default: m.CodeIcon }))),
  Collection30Icon: lazy(() =>
    import('../Collection30Icon').then(m => ({ default: m.Collection30Icon })),
  ),
  ColorProfileStyleThemeIcon: lazy(() =>
    import('../ColorProfileStyleThemeIcon').then(m => ({ default: m.ColorProfileStyleThemeIcon })),
  ),
  ColumnIcon: lazy(() => import('../ColumnIcon').then(m => ({ default: m.ColumnIcon }))),
  CommentIcon: lazy(() => import('../CommentIcon').then(m => ({ default: m.CommentIcon }))),
  CompareIcon: lazy(() => import('../CompareIcon').then(m => ({ default: m.CompareIcon }))),
  ComputationHistoriesIcon: lazy(() =>
    import('../ComputationHistoriesIcon').then(m => ({ default: m.ComputationHistoriesIcon })),
  ),
  ConfirmationNumberIcon: lazy(() =>
    import('../ConfirmationNumberIcon').then(m => ({ default: m.ConfirmationNumberIcon })),
  ),
  ConnectOneNIcon: lazy(() =>
    import('../ConnectOneNIcon').then(m => ({ default: m.ConnectOneNIcon })),
  ),
  ConnectOneOneIcon: lazy(() =>
    import('../ConnectOneOneIcon').then(m => ({ default: m.ConnectOneOneIcon })),
  ),
  Connectors30Icon: lazy(() =>
    import('../Connectors30Icon').then(m => ({ default: m.Connectors30Icon })),
  ),
  Content30Icon: lazy(() => import('../Content30Icon').then(m => ({ default: m.Content30Icon }))),
  ConversionEvent30Icon: lazy(() =>
    import('../ConversionEvent30Icon').then(m => ({ default: m.ConversionEvent30Icon })),
  ),
  ConversionIcon: lazy(() =>
    import('../ConversionIcon').then(m => ({ default: m.ConversionIcon })),
  ),
  CopyDuplicateIcon: lazy(() =>
    import('../CopyDuplicateIcon').then(m => ({ default: m.CopyDuplicateIcon })),
  ),
  CopyLinkActiveIcon: lazy(() =>
    import('../CopyLinkActiveIcon').then(m => ({ default: m.CopyLinkActiveIcon })),
  ),
  CopyLinkIcon: lazy(() => import('../CopyLinkIcon').then(m => ({ default: m.CopyLinkIcon }))),
  CropIcon: lazy(() => import('../CropIcon').then(m => ({ default: m.CropIcon }))),
  CursorIcon: lazy(() => import('../CursorIcon').then(m => ({ default: m.CursorIcon }))),
  CurvedConnectorIcon: lazy(() =>
    import('../CurvedConnectorIcon').then(m => ({ default: m.CurvedConnectorIcon })),
  ),
  CustomerIcon: lazy(() => import('../CustomerIcon').then(m => ({ default: m.CustomerIcon }))),
  CustomerInformationIcon: lazy(() =>
    import('../CustomerInformationIcon').then(m => ({ default: m.CustomerInformationIcon })),
  ),
  CustomerJourney30Icon: lazy(() =>
    import('../CustomerJourney30Icon').then(m => ({ default: m.CustomerJourney30Icon })),
  ),
  CustomerJourneyIcon: lazy(() =>
    import('../CustomerJourneyIcon').then(m => ({ default: m.CustomerJourneyIcon })),
  ),
  Customers30Icon: lazy(() =>
    import('../Customers30Icon').then(m => ({ default: m.Customers30Icon })),
  ),
  Dashboard30Icon: lazy(() =>
    import('../Dashboard30Icon').then(m => ({ default: m.Dashboard30Icon })),
  ),
  DashboardIcon: lazy(() => import('../DashboardIcon').then(m => ({ default: m.DashboardIcon }))),
  DataDestination30Icon: lazy(() =>
    import('../DataDestination30Icon').then(m => ({ default: m.DataDestination30Icon })),
  ),
  DataDestinationIcon: lazy(() =>
    import('../DataDestinationIcon').then(m => ({ default: m.DataDestinationIcon })),
  ),
  DataEncryption30Icon: lazy(() =>
    import('../DataEncryption30Icon').then(m => ({ default: m.DataEncryption30Icon })),
  ),
  DataEncryptionIcon: lazy(() =>
    import('../DataEncryptionIcon').then(m => ({ default: m.DataEncryptionIcon })),
  ),
  DataIcon: lazy(() => import('../DataIcon').then(m => ({ default: m.DataIcon }))),
  DataRefreshIcon: lazy(() =>
    import('../DataRefreshIcon').then(m => ({ default: m.DataRefreshIcon })),
  ),
  DataSchema30Icon: lazy(() =>
    import('../DataSchema30Icon').then(m => ({ default: m.DataSchema30Icon })),
  ),
  DataSource30Icon: lazy(() =>
    import('../DataSource30Icon').then(m => ({ default: m.DataSource30Icon })),
  ),
  DataView30Icon: lazy(() =>
    import('../DataView30Icon').then(m => ({ default: m.DataView30Icon })),
  ),
  DataViewIcon: lazy(() => import('../DataViewIcon').then(m => ({ default: m.DataViewIcon }))),
  Dataflows30Icon: lazy(() =>
    import('../Dataflows30Icon').then(m => ({ default: m.Dataflows30Icon })),
  ),
  DatasourceIcon: lazy(() =>
    import('../DatasourceIcon').then(m => ({ default: m.DatasourceIcon })),
  ),
  DatasourceSegment30Icon: lazy(() =>
    import('../DatasourceSegment30Icon').then(m => ({ default: m.DatasourceSegment30Icon })),
  ),
  DecreaseDecimalIcon: lazy(() =>
    import('../DecreaseDecimalIcon').then(m => ({ default: m.DecreaseDecimalIcon })),
  ),
  DeleteRemoveTrashIcon: lazy(() =>
    import('../DeleteRemoveTrashIcon').then(m => ({ default: m.DeleteRemoveTrashIcon })),
  ),
  DeliveryLogIcon: lazy(() =>
    import('../DeliveryLogIcon').then(m => ({ default: m.DeliveryLogIcon })),
  ),
  DesktopLaptopIcon: lazy(() =>
    import('../DesktopLaptopIcon').then(m => ({ default: m.DesktopLaptopIcon })),
  ),
  DesktopWindowsIcon: lazy(() =>
    import('../DesktopWindowsIcon').then(m => ({ default: m.DesktopWindowsIcon })),
  ),
  Destination130Icon: lazy(() =>
    import('../Destination130Icon').then(m => ({ default: m.Destination130Icon })),
  ),
  Destinations30Icon: lazy(() =>
    import('../Destinations30Icon').then(m => ({ default: m.Destinations30Icon })),
  ),
  DestinationsIcon: lazy(() =>
    import('../DestinationsIcon').then(m => ({ default: m.DestinationsIcon })),
  ),
  Diagram30Icon: lazy(() => import('../Diagram30Icon').then(m => ({ default: m.Diagram30Icon }))),
  DiagramIcon: lazy(() => import('../DiagramIcon').then(m => ({ default: m.DiagramIcon }))),
  DirectoriesIcon: lazy(() =>
    import('../DirectoriesIcon').then(m => ({ default: m.DirectoriesIcon })),
  ),
  DisplayMonitorScreenIcon: lazy(() =>
    import('../DisplayMonitorScreenIcon').then(m => ({ default: m.DisplayMonitorScreenIcon })),
  ),
  DomainManagementIcon: lazy(() =>
    import('../DomainManagementIcon').then(m => ({ default: m.DomainManagementIcon })),
  ),
  DoubeThreeDotIcon: lazy(() =>
    import('../DoubeThreeDotIcon').then(m => ({ default: m.DoubeThreeDotIcon })),
  ),
  DoubleTreeDotIcon: lazy(() =>
    import('../DoubleTreeDotIcon').then(m => ({ default: m.DoubleTreeDotIcon })),
  ),
  DownloadIcon: lazy(() => import('../DownloadIcon').then(m => ({ default: m.DownloadIcon }))),
  DraftDocumentIcon: lazy(() =>
    import('../DraftDocumentIcon').then(m => ({ default: m.DraftDocumentIcon })),
  ),
  DuplicateIcon: lazy(() => import('../DuplicateIcon').then(m => ({ default: m.DuplicateIcon }))),
  DynamicFormIcon: lazy(() =>
    import('../DynamicFormIcon').then(m => ({ default: m.DynamicFormIcon })),
  ),
  DynamicImageIcon: lazy(() =>
    import('../DynamicImageIcon').then(m => ({ default: m.DynamicImageIcon })),
  ),
  DynamicTextIcon: lazy(() =>
    import('../DynamicTextIcon').then(m => ({ default: m.DynamicTextIcon })),
  ),
  EditFilledIcon: lazy(() =>
    import('../EditFilledIcon').then(m => ({ default: m.EditFilledIcon })),
  ),
  EditIcon: lazy(() => import('../EditIcon').then(m => ({ default: m.EditIcon }))),
  ElbowConnectorIcon: lazy(() =>
    import('../ElbowConnectorIcon').then(m => ({ default: m.ElbowConnectorIcon })),
  ),
  EmailTemplate30Icon: lazy(() =>
    import('../EmailTemplate30Icon').then(m => ({ default: m.EmailTemplate30Icon })),
  ),
  EmailTemplateIcon: lazy(() =>
    import('../EmailTemplateIcon').then(m => ({ default: m.EmailTemplateIcon })),
  ),
  EmailTemplateMenuIcon: lazy(() =>
    import('../EmailTemplateMenuIcon').then(m => ({ default: m.EmailTemplateMenuIcon })),
  ),
  EmojiAirportShuttleIcon: lazy(() =>
    import('../EmojiAirportShuttleIcon').then(m => ({ default: m.EmojiAirportShuttleIcon })),
  ),
  EmojiBallIcon: lazy(() => import('../EmojiBallIcon').then(m => ({ default: m.EmojiBallIcon }))),
  EmojiBearIcon: lazy(() => import('../EmojiBearIcon').then(m => ({ default: m.EmojiBearIcon }))),
  EmojiElectricBoltIcon: lazy(() =>
    import('../EmojiElectricBoltIcon').then(m => ({ default: m.EmojiElectricBoltIcon })),
  ),
  EmojiEmotionsIcon: lazy(() =>
    import('../EmojiEmotionsIcon').then(m => ({ default: m.EmojiEmotionsIcon })),
  ),
  EmojiFlagIcon: lazy(() => import('../EmojiFlagIcon').then(m => ({ default: m.EmojiFlagIcon }))),
  EmojiLaughIcon: lazy(() =>
    import('../EmojiLaughIcon').then(m => ({ default: m.EmojiLaughIcon })),
  ),
  EmojiLightBulbIcon: lazy(() =>
    import('../EmojiLightBulbIcon').then(m => ({ default: m.EmojiLightBulbIcon })),
  ),
  EmojiPetFootIcon: lazy(() =>
    import('../EmojiPetFootIcon').then(m => ({ default: m.EmojiPetFootIcon })),
  ),
  EmojiRamenDiningIcon: lazy(() =>
    import('../EmojiRamenDiningIcon').then(m => ({ default: m.EmojiRamenDiningIcon })),
  ),
  EmojiReactionIcon: lazy(() =>
    import('../EmojiReactionIcon').then(m => ({ default: m.EmojiReactionIcon })),
  ),
  EmojiSmileIcon: lazy(() =>
    import('../EmojiSmileIcon').then(m => ({ default: m.EmojiSmileIcon })),
  ),
  ErrorIcon: lazy(() => import('../ErrorIcon').then(m => ({ default: m.ErrorIcon }))),
  EventAttribute30Icon: lazy(() =>
    import('../EventAttribute30Icon').then(m => ({ default: m.EventAttribute30Icon })),
  ),
  EventAttributeIcon: lazy(() =>
    import('../EventAttributeIcon').then(m => ({ default: m.EventAttributeIcon })),
  ),
  EventIcon: lazy(() => import('../EventIcon').then(m => ({ default: m.EventIcon }))),
  EventSources30Icon: lazy(() =>
    import('../EventSources30Icon').then(m => ({ default: m.EventSources30Icon })),
  ),
  EventSourcesIcon: lazy(() =>
    import('../EventSourcesIcon').then(m => ({ default: m.EventSourcesIcon })),
  ),
  ExcludePeopleIcon: lazy(() =>
    import('../ExcludePeopleIcon').then(m => ({ default: m.ExcludePeopleIcon })),
  ),
  ExitFullScreenIcon: lazy(() =>
    import('../ExitFullScreenIcon').then(m => ({ default: m.ExitFullScreenIcon })),
  ),
  ExitLogOutIcon: lazy(() =>
    import('../ExitLogOutIcon').then(m => ({ default: m.ExitLogOutIcon })),
  ),
  ExpandLessIcon: lazy(() =>
    import('../ExpandLessIcon').then(m => ({ default: m.ExpandLessIcon })),
  ),
  ExpandMoreIcon: lazy(() =>
    import('../ExpandMoreIcon').then(m => ({ default: m.ExpandMoreIcon })),
  ),
  ExpandMoreRightIcon: lazy(() =>
    import('../ExpandMoreRightIcon').then(m => ({ default: m.ExpandMoreRightIcon })),
  ),
  ExpandViewFullIcon: lazy(() =>
    import('../ExpandViewFullIcon').then(m => ({ default: m.ExpandViewFullIcon })),
  ),
  ExploreBuiltIn30Icon: lazy(() =>
    import('../ExploreBuiltIn30Icon').then(m => ({ default: m.ExploreBuiltIn30Icon })),
  ),
  ExploreBuiltInIcon: lazy(() =>
    import('../ExploreBuiltInIcon').then(m => ({ default: m.ExploreBuiltInIcon })),
  ),
  ExploreIcon: lazy(() => import('../ExploreIcon').then(m => ({ default: m.ExploreIcon }))),
  ExploreSegmentData30Icon: lazy(() =>
    import('../ExploreSegmentData30Icon').then(m => ({ default: m.ExploreSegmentData30Icon })),
  ),
  ExploreStandard30Icon: lazy(() =>
    import('../ExploreStandard30Icon').then(m => ({ default: m.ExploreStandard30Icon })),
  ),
  ExploreStandardIcon: lazy(() =>
    import('../ExploreStandardIcon').then(m => ({ default: m.ExploreStandardIcon })),
  ),
  ExportHtmlIcon: lazy(() =>
    import('../ExportHtmlIcon').then(m => ({ default: m.ExportHtmlIcon })),
  ),
  ExportMenuIcon: lazy(() =>
    import('../ExportMenuIcon').then(m => ({ default: m.ExportMenuIcon })),
  ),
  ExtendValueIcon: lazy(() =>
    import('../ExtendValueIcon').then(m => ({ default: m.ExtendValueIcon })),
  ),
  FactCheckIcon: lazy(() => import('../FactCheckIcon').then(m => ({ default: m.FactCheckIcon }))),
  FileDownloadIcon: lazy(() =>
    import('../FileDownloadIcon').then(m => ({ default: m.FileDownloadIcon })),
  ),
  FileTransferIcon: lazy(() =>
    import('../FileTransferIcon').then(m => ({ default: m.FileTransferIcon })),
  ),
  FileUpload1Icon: lazy(() =>
    import('../FileUpload1Icon').then(m => ({ default: m.FileUpload1Icon })),
  ),
  FileUploadIcon: lazy(() =>
    import('../FileUploadIcon').then(m => ({ default: m.FileUploadIcon })),
  ),
  FilterIcon: lazy(() => import('../FilterIcon').then(m => ({ default: m.FilterIcon }))),
  FolderCreateNewFolderIcon: lazy(() =>
    import('../FolderCreateNewFolderIcon').then(m => ({ default: m.FolderCreateNewFolderIcon })),
  ),
  FolderShareIcon: lazy(() =>
    import('../FolderShareIcon').then(m => ({ default: m.FolderShareIcon })),
  ),
  FolderUploadIcon: lazy(() =>
    import('../FolderUploadIcon').then(m => ({ default: m.FolderUploadIcon })),
  ),
  ForceRunIcon: lazy(() => import('../ForceRunIcon').then(m => ({ default: m.ForceRunIcon }))),
  ForecastIcon: lazy(() => import('../ForecastIcon').then(m => ({ default: m.ForecastIcon }))),
  Form30Icon: lazy(() => import('../Form30Icon').then(m => ({ default: m.Form30Icon }))),
  ForwardIcon: lazy(() => import('../ForwardIcon').then(m => ({ default: m.ForwardIcon }))),
  FreeDrawIcon: lazy(() => import('../FreeDrawIcon').then(m => ({ default: m.FreeDrawIcon }))),
  FullReviewIcon: lazy(() =>
    import('../FullReviewIcon').then(m => ({ default: m.FullReviewIcon })),
  ),
  GPTIcon: lazy(() => import('../GPTIcon').then(m => ({ default: m.GPTIcon }))),
  GalleryTemplateIcon: lazy(() =>
    import('../GalleryTemplateIcon').then(m => ({ default: m.GalleryTemplateIcon })),
  ),
  GeneralSettingIcon: lazy(() =>
    import('../GeneralSettingIcon').then(m => ({ default: m.GeneralSettingIcon })),
  ),
  GeneralSettings30Icon: lazy(() =>
    import('../GeneralSettings30Icon').then(m => ({ default: m.GeneralSettings30Icon })),
  ),
  GenerateAutoWizardIcon: lazy(() =>
    import('../GenerateAutoWizardIcon').then(m => ({ default: m.GenerateAutoWizardIcon })),
  ),
  Geofence30Icon: lazy(() =>
    import('../Geofence30Icon').then(m => ({ default: m.Geofence30Icon })),
  ),
  GetInsightIcon: lazy(() =>
    import('../GetInsightIcon').then(m => ({ default: m.GetInsightIcon })),
  ),
  GridViewIcon: lazy(() => import('../GridViewIcon').then(m => ({ default: m.GridViewIcon }))),
  GroupIcon: lazy(() => import('../GroupIcon').then(m => ({ default: m.GroupIcon }))),
  GroupLayerIcon: lazy(() =>
    import('../GroupLayerIcon').then(m => ({ default: m.GroupLayerIcon })),
  ),
  HeartFavoriteBorderIcon: lazy(() =>
    import('../HeartFavoriteBorderIcon').then(m => ({ default: m.HeartFavoriteBorderIcon })),
  ),
  HeartFavoriteIcon: lazy(() =>
    import('../HeartFavoriteIcon').then(m => ({ default: m.HeartFavoriteIcon })),
  ),
  HelpIcon: lazy(() => import('../HelpIcon').then(m => ({ default: m.HelpIcon }))),
  HighlightAltIcon: lazy(() =>
    import('../HighlightAltIcon').then(m => ({ default: m.HighlightAltIcon })),
  ),
  HighlightIcon: lazy(() => import('../HighlightIcon').then(m => ({ default: m.HighlightIcon }))),
  HomeHouse30Icon: lazy(() =>
    import('../HomeHouse30Icon').then(m => ({ default: m.HomeHouse30Icon })),
  ),
  HomeIcon: lazy(() => import('../HomeIcon').then(m => ({ default: m.HomeIcon }))),
  HowToRegIcon: lazy(() => import('../HowToRegIcon').then(m => ({ default: m.HowToRegIcon }))),
  IdeaIcon: lazy(() => import('../IdeaIcon').then(m => ({ default: m.IdeaIcon }))),
  IdentityGraphIcon: lazy(() =>
    import('../IdentityGraphIcon').then(m => ({ default: m.IdentityGraphIcon })),
  ),
  ImageIcon: lazy(() => import('../ImageIcon').then(m => ({ default: m.ImageIcon }))),
  InactiveIcon: lazy(() => import('../InactiveIcon').then(m => ({ default: m.InactiveIcon }))),
  InactiveVariantIcon: lazy(() =>
    import('../InactiveVariantIcon').then(m => ({ default: m.InactiveVariantIcon })),
  ),
  IncludePeopleIcon: lazy(() =>
    import('../IncludePeopleIcon').then(m => ({ default: m.IncludePeopleIcon })),
  ),
  IncreaseDecimalIcon: lazy(() =>
    import('../IncreaseDecimalIcon').then(m => ({ default: m.IncreaseDecimalIcon })),
  ),
  IncreaseDecreaseIcon: lazy(() =>
    import('../IncreaseDecreaseIcon').then(m => ({ default: m.IncreaseDecreaseIcon })),
  ),
  Integration30Icon: lazy(() =>
    import('../Integration30Icon').then(m => ({ default: m.Integration30Icon })),
  ),
  InteractiveIcon: lazy(() =>
    import('../InteractiveIcon').then(m => ({ default: m.InteractiveIcon })),
  ),
  InvisibleIcon: lazy(() => import('../InvisibleIcon').then(m => ({ default: m.InvisibleIcon }))),
  InvitePeopleIcon: lazy(() =>
    import('../InvitePeopleIcon').then(m => ({ default: m.InvitePeopleIcon })),
  ),
  IpRestrictionIcon: lazy(() =>
    import('../IpRestrictionIcon').then(m => ({ default: m.IpRestrictionIcon })),
  ),
  IssueBugIcon: lazy(() => import('../IssueBugIcon').then(m => ({ default: m.IssueBugIcon }))),
  ItemRecommendationIcon: lazy(() =>
    import('../ItemRecommendationIcon').then(m => ({ default: m.ItemRecommendationIcon })),
  ),
  JourneyTactic30Icon: lazy(() =>
    import('../JourneyTactic30Icon').then(m => ({ default: m.JourneyTactic30Icon })),
  ),
  JourneyTacticIcon: lazy(() =>
    import('../JourneyTacticIcon').then(m => ({ default: m.JourneyTacticIcon })),
  ),
  LayerIcon: lazy(() => import('../LayerIcon').then(m => ({ default: m.LayerIcon }))),
  LeftExpandIcon: lazy(() =>
    import('../LeftExpandIcon').then(m => ({ default: m.LeftExpandIcon })),
  ),
  LibraryIcon: lazy(() => import('../LibraryIcon').then(m => ({ default: m.LibraryIcon }))),
  LineIcon: lazy(() => import('../LineIcon').then(m => ({ default: m.LineIcon }))),
  LinkManagement30Icon: lazy(() =>
    import('../LinkManagement30Icon').then(m => ({ default: m.LinkManagement30Icon })),
  ),
  LinkManagementIcon: lazy(() =>
    import('../LinkManagementIcon').then(m => ({ default: m.LinkManagementIcon })),
  ),
  LinkOffIcon: lazy(() => import('../LinkOffIcon').then(m => ({ default: m.LinkOffIcon }))),
  ListIcon: lazy(() => import('../ListIcon').then(m => ({ default: m.ListIcon }))),
  LoadingIcon: lazy(() => import('../LoadingIcon').then(m => ({ default: m.LoadingIcon }))),
  LocalMallIcon: lazy(() => import('../LocalMallIcon').then(m => ({ default: m.LocalMallIcon }))),
  LongerIcon: lazy(() => import('../LongerIcon').then(m => ({ default: m.LongerIcon }))),
  MapIcon: lazy(() => import('../MapIcon').then(m => ({ default: m.MapIcon }))),
  MarkAsReadIcon: lazy(() =>
    import('../MarkAsReadIcon').then(m => ({ default: m.MarkAsReadIcon })),
  ),
  MarketingHub30Icon: lazy(() =>
    import('../MarketingHub30Icon').then(m => ({ default: m.MarketingHub30Icon })),
  ),
  MediaGalleryIcon: lazy(() =>
    import('../MediaGalleryIcon').then(m => ({ default: m.MediaGalleryIcon })),
  ),
  MediaTemplate30Icon: lazy(() =>
    import('../MediaTemplate30Icon').then(m => ({ default: m.MediaTemplate30Icon })),
  ),
  MediaTemplateIcon: lazy(() =>
    import('../MediaTemplateIcon').then(m => ({ default: m.MediaTemplateIcon })),
  ),
  MediaTemplateMenuIcon: lazy(() =>
    import('../MediaTemplateMenuIcon').then(m => ({ default: m.MediaTemplateMenuIcon })),
  ),
  Menu30Icon: lazy(() => import('../Menu30Icon').then(m => ({ default: m.Menu30Icon }))),
  MenuCollapseIcon: lazy(() =>
    import('../MenuCollapseIcon').then(m => ({ default: m.MenuCollapseIcon })),
  ),
  MenuExpandIcon: lazy(() =>
    import('../MenuExpandIcon').then(m => ({ default: m.MenuExpandIcon })),
  ),
  MenuImageMultiIcon: lazy(() =>
    import('../MenuImageMultiIcon').then(m => ({ default: m.MenuImageMultiIcon })),
  ),
  MenuImageSingleIcon: lazy(() =>
    import('../MenuImageSingleIcon').then(m => ({ default: m.MenuImageSingleIcon })),
  ),
  MergeIcon: lazy(() => import('../MergeIcon').then(m => ({ default: m.MergeIcon }))),
  MinusIcon: lazy(() => import('../MinusIcon').then(m => ({ default: m.MinusIcon }))),
  MobileFriendlyIcon: lazy(() =>
    import('../MobileFriendlyIcon').then(m => ({ default: m.MobileFriendlyIcon })),
  ),
  MobilePhoneIcon: lazy(() =>
    import('../MobilePhoneIcon').then(m => ({ default: m.MobilePhoneIcon })),
  ),
  MonitorAppIcon: lazy(() =>
    import('../MonitorAppIcon').then(m => ({ default: m.MonitorAppIcon })),
  ),
  MoreIcon: lazy(() => import('../MoreIcon').then(m => ({ default: m.MoreIcon }))),
  MoreInfoIcon: lazy(() => import('../MoreInfoIcon').then(m => ({ default: m.MoreInfoIcon }))),
  MoveToIcon: lazy(() => import('../MoveToIcon').then(m => ({ default: m.MoveToIcon }))),
  MuteIcon: lazy(() => import('../MuteIcon').then(m => ({ default: m.MuteIcon }))),
  NodeViewIcon: lazy(() => import('../NodeViewIcon').then(m => ({ default: m.NodeViewIcon }))),
  NotificationIcon: lazy(() =>
    import('../NotificationIcon').then(m => ({ default: m.NotificationIcon })),
  ),
  NotificationSettingIcon: lazy(() =>
    import('../NotificationSettingIcon').then(m => ({ default: m.NotificationSettingIcon })),
  ),
  NotificationsActiveIcon: lazy(() =>
    import('../NotificationsActiveIcon').then(m => ({ default: m.NotificationsActiveIcon })),
  ),
  OpenNewTabIcon: lazy(() =>
    import('../OpenNewTabIcon').then(m => ({ default: m.OpenNewTabIcon })),
  ),
  OpenUrlIcon: lazy(() => import('../OpenUrlIcon').then(m => ({ default: m.OpenUrlIcon }))),
  OrchestrationIcon: lazy(() =>
    import('../OrchestrationIcon').then(m => ({ default: m.OrchestrationIcon })),
  ),
  PageArticleIcon: lazy(() =>
    import('../PageArticleIcon').then(m => ({ default: m.PageArticleIcon })),
  ),
  PauseIcon: lazy(() => import('../PauseIcon').then(m => ({ default: m.PauseIcon }))),
  PaymentIcon: lazy(() => import('../PaymentIcon').then(m => ({ default: m.PaymentIcon }))),
  PenDrawingIcon: lazy(() =>
    import('../PenDrawingIcon').then(m => ({ default: m.PenDrawingIcon })),
  ),
  PenRulerIcon: lazy(() => import('../PenRulerIcon').then(m => ({ default: m.PenRulerIcon }))),
  PersonAddDisabledIcon: lazy(() =>
    import('../PersonAddDisabledIcon').then(m => ({ default: m.PersonAddDisabledIcon })),
  ),
  PersonUserAccountIcon: lazy(() =>
    import('../PersonUserAccountIcon').then(m => ({ default: m.PersonUserAccountIcon })),
  ),
  PhoneCallbackIcon: lazy(() =>
    import('../PhoneCallbackIcon').then(m => ({ default: m.PhoneCallbackIcon })),
  ),
  PhoneIphoneIcon: lazy(() =>
    import('../PhoneIphoneIcon').then(m => ({ default: m.PhoneIphoneIcon })),
  ),
  PivotTableChartIcon: lazy(() =>
    import('../PivotTableChartIcon').then(m => ({ default: m.PivotTableChartIcon })),
  ),
  PlaneIcon: lazy(() => import('../PlaneIcon').then(m => ({ default: m.PlaneIcon }))),
  Plannings30Icon: lazy(() =>
    import('../Plannings30Icon').then(m => ({ default: m.Plannings30Icon })),
  ),
  Predictive30Icon: lazy(() =>
    import('../Predictive30Icon').then(m => ({ default: m.Predictive30Icon })),
  ),
  PredictiveModel30Icon: lazy(() =>
    import('../PredictiveModel30Icon').then(m => ({ default: m.PredictiveModel30Icon })),
  ),
  PredictiveModelIcon: lazy(() =>
    import('../PredictiveModelIcon').then(m => ({ default: m.PredictiveModelIcon })),
  ),
  PreviewIcon: lazy(() => import('../PreviewIcon').then(m => ({ default: m.PreviewIcon }))),
  PreviewVisibilityEyeIcon: lazy(() =>
    import('../PreviewVisibilityEyeIcon').then(m => ({ default: m.PreviewVisibilityEyeIcon })),
  ),
  ProcessHistoryIcon: lazy(() =>
    import('../ProcessHistoryIcon').then(m => ({ default: m.ProcessHistoryIcon })),
  ),
  ProfileTemplate30Icon: lazy(() =>
    import('../ProfileTemplate30Icon').then(m => ({ default: m.ProfileTemplate30Icon })),
  ),
  ProfileTemplateIcon: lazy(() =>
    import('../ProfileTemplateIcon').then(m => ({ default: m.ProfileTemplateIcon })),
  ),
  ProjectIcon: lazy(() => import('../ProjectIcon').then(m => ({ default: m.ProjectIcon }))),
  PromotionCenter30Icon: lazy(() =>
    import('../PromotionCenter30Icon').then(m => ({ default: m.PromotionCenter30Icon })),
  ),
  PromotionCenterIcon: lazy(() =>
    import('../PromotionCenterIcon').then(m => ({ default: m.PromotionCenterIcon })),
  ),
  Providers30Icon: lazy(() =>
    import('../Providers30Icon').then(m => ({ default: m.Providers30Icon })),
  ),
  PublicIcon: lazy(() => import('../PublicIcon').then(m => ({ default: m.PublicIcon }))),
  PublishSendIcon: lazy(() =>
    import('../PublishSendIcon').then(m => ({ default: m.PublishSendIcon })),
  ),
  QueryIcon: lazy(() => import('../QueryIcon').then(m => ({ default: m.QueryIcon }))),
  QuickTestIcon: lazy(() => import('../QuickTestIcon').then(m => ({ default: m.QuickTestIcon }))),
  RatingStarBorderIcon: lazy(() =>
    import('../RatingStarBorderIcon').then(m => ({ default: m.RatingStarBorderIcon })),
  ),
  RatingStarFullIcon: lazy(() =>
    import('../RatingStarFullIcon').then(m => ({ default: m.RatingStarFullIcon })),
  ),
  RatingStarHalfIcon: lazy(() =>
    import('../RatingStarHalfIcon').then(m => ({ default: m.RatingStarHalfIcon })),
  ),
  RealtimeStreaming30Icon: lazy(() =>
    import('../RealtimeStreaming30Icon').then(m => ({ default: m.RealtimeStreaming30Icon })),
  ),
  RealtimeStreamingIcon: lazy(() =>
    import('../RealtimeStreamingIcon').then(m => ({ default: m.RealtimeStreamingIcon })),
  ),
  RecentHistoryVersionHistoryIcon: lazy(() =>
    import('../RecentHistoryVersionHistoryIcon').then(m => ({
      default: m.RecentHistoryVersionHistoryIcon,
    })),
  ),
  RectangleIcon: lazy(() => import('../RectangleIcon').then(m => ({ default: m.RectangleIcon }))),
  RedeemIcon: lazy(() => import('../RedeemIcon').then(m => ({ default: m.RedeemIcon }))),
  RedoIcon: lazy(() => import('../RedoIcon').then(m => ({ default: m.RedoIcon }))),
  RefreshIcon: lazy(() => import('../RefreshIcon').then(m => ({ default: m.RefreshIcon }))),
  RemoveRedEyeIcon: lazy(() =>
    import('../RemoveRedEyeIcon').then(m => ({ default: m.RemoveRedEyeIcon })),
  ),
  RemoveShoppingCartIcon: lazy(() =>
    import('../RemoveShoppingCartIcon').then(m => ({ default: m.RemoveShoppingCartIcon })),
  ),
  ReportIcon: lazy(() => import('../ReportIcon').then(m => ({ default: m.ReportIcon }))),
  RequestIcon: lazy(() => import('../RequestIcon').then(m => ({ default: m.RequestIcon }))),
  RequiredIcon: lazy(() => import('../RequiredIcon').then(m => ({ default: m.RequiredIcon }))),
  ResendValidateEmailIcon: lazy(() =>
    import('../ResendValidateEmailIcon').then(m => ({ default: m.ResendValidateEmailIcon })),
  ),
  RestoreDefaultIcon: lazy(() =>
    import('../RestoreDefaultIcon').then(m => ({ default: m.RestoreDefaultIcon })),
  ),
  ResumeIcon: lazy(() => import('../ResumeIcon').then(m => ({ default: m.ResumeIcon }))),
  RightExpandIcon: lazy(() =>
    import('../RightExpandIcon').then(m => ({ default: m.RightExpandIcon })),
  ),
  Role30Icon: lazy(() => import('../Role30Icon').then(m => ({ default: m.Role30Icon }))),
  SQLWorkspace30Icon: lazy(() =>
    import('../SQLWorkspace30Icon').then(m => ({ default: m.SQLWorkspace30Icon })),
  ),
  SaveAsIcon: lazy(() => import('../SaveAsIcon').then(m => ({ default: m.SaveAsIcon }))),
  SaveIcon: lazy(() => import('../SaveIcon').then(m => ({ default: m.SaveIcon }))),
  ScheduleEmailDeliveryIcon: lazy(() =>
    import('../ScheduleEmailDeliveryIcon').then(m => ({ default: m.ScheduleEmailDeliveryIcon })),
  ),
  ScreenshotMonitorIcon: lazy(() =>
    import('../ScreenshotMonitorIcon').then(m => ({ default: m.ScreenshotMonitorIcon })),
  ),
  SearchIcon: lazy(() => import('../SearchIcon').then(m => ({ default: m.SearchIcon }))),
  SectionIcon: lazy(() => import('../SectionIcon').then(m => ({ default: m.SectionIcon }))),
  Segments30Icon: lazy(() =>
    import('../Segments30Icon').then(m => ({ default: m.Segments30Icon })),
  ),
  SegmentsIcon: lazy(() => import('../SegmentsIcon').then(m => ({ default: m.SegmentsIcon }))),
  SendTranscriptIcon: lazy(() =>
    import('../SendTranscriptIcon').then(m => ({ default: m.SendTranscriptIcon })),
  ),
  SettingFillIcon: lazy(() =>
    import('../SettingFillIcon').then(m => ({ default: m.SettingFillIcon })),
  ),
  SettingIcon: lazy(() => import('../SettingIcon').then(m => ({ default: m.SettingIcon }))),
  ShapeCircleIcon: lazy(() =>
    import('../ShapeCircleIcon').then(m => ({ default: m.ShapeCircleIcon })),
  ),
  ShapeSquareIcon: lazy(() =>
    import('../ShapeSquareIcon').then(m => ({ default: m.ShapeSquareIcon })),
  ),
  ShareIcon: lazy(() => import('../ShareIcon').then(m => ({ default: m.ShareIcon }))),
  ShareWithMeAccountIcon: lazy(() =>
    import('../ShareWithMeAccountIcon').then(m => ({ default: m.ShareWithMeAccountIcon })),
  ),
  ShoppingBagIcon: lazy(() =>
    import('../ShoppingBagIcon').then(m => ({ default: m.ShoppingBagIcon })),
  ),
  ShoppingCartIcon: lazy(() =>
    import('../ShoppingCartIcon').then(m => ({ default: m.ShoppingCartIcon })),
  ),
  ShowLeftPanelIcon: lazy(() =>
    import('../ShowLeftPanelIcon').then(m => ({ default: m.ShowLeftPanelIcon })),
  ),
  ShowRightPanelIcon: lazy(() =>
    import('../ShowRightPanelIcon').then(m => ({ default: m.ShowRightPanelIcon })),
  ),
  SignatureIcon: lazy(() => import('../SignatureIcon').then(m => ({ default: m.SignatureIcon }))),
  SlideGroupIcon: lazy(() =>
    import('../SlideGroupIcon').then(m => ({ default: m.SlideGroupIcon })),
  ),
  Sort1Icon: lazy(() => import('../Sort1Icon').then(m => ({ default: m.Sort1Icon }))),
  SortIcon: lazy(() => import('../SortIcon').then(m => ({ default: m.SortIcon }))),
  SqlWorkspaceIcon: lazy(() =>
    import('../SqlWorkspaceIcon').then(m => ({ default: m.SqlWorkspaceIcon })),
  ),
  StatisticIcon: lazy(() => import('../StatisticIcon').then(m => ({ default: m.StatisticIcon }))),
  StatusInfoIcon: lazy(() =>
    import('../StatusInfoIcon').then(m => ({ default: m.StatusInfoIcon })),
  ),
  StickerIcon: lazy(() => import('../StickerIcon').then(m => ({ default: m.StickerIcon }))),
  StopRecordIcon: lazy(() =>
    import('../StopRecordIcon').then(m => ({ default: m.StopRecordIcon })),
  ),
  StoreIcon: lazy(() => import('../StoreIcon').then(m => ({ default: m.StoreIcon }))),
  StudioTemplates30Icon: lazy(() =>
    import('../StudioTemplates30Icon').then(m => ({ default: m.StudioTemplates30Icon })),
  ),
  SubtractRadiusIcon: lazy(() =>
    import('../SubtractRadiusIcon').then(m => ({ default: m.SubtractRadiusIcon })),
  ),
  SupportAssistantIcon: lazy(() =>
    import('../SupportAssistantIcon').then(m => ({ default: m.SupportAssistantIcon })),
  ),
  Survey30Icon: lazy(() => import('../Survey30Icon').then(m => ({ default: m.Survey30Icon }))),
  SurveyDirectorProfileIcon: lazy(() =>
    import('../SurveyDirectorProfileIcon').then(m => ({ default: m.SurveyDirectorProfileIcon })),
  ),
  SurveyIcon: lazy(() => import('../SurveyIcon').then(m => ({ default: m.SurveyIcon }))),
  SurveyProject30Icon: lazy(() =>
    import('../SurveyProject30Icon').then(m => ({ default: m.SurveyProject30Icon })),
  ),
  SystemRunningIcon: lazy(() =>
    import('../SystemRunningIcon').then(m => ({ default: m.SystemRunningIcon })),
  ),
  TabGroupIcon: lazy(() => import('../TabGroupIcon').then(m => ({ default: m.TabGroupIcon }))),
  TableIcon: lazy(() => import('../TableIcon').then(m => ({ default: m.TableIcon }))),
  TableVer2Icon: lazy(() => import('../TableVer2Icon').then(m => ({ default: m.TableVer2Icon }))),
  TaskAltIcon: lazy(() => import('../TaskAltIcon').then(m => ({ default: m.TaskAltIcon }))),
  TextIcon: lazy(() => import('../TextIcon').then(m => ({ default: m.TextIcon }))),
  ThirdPartyPluginV130Icon: lazy(() =>
    import('../ThirdPartyPluginV130Icon').then(m => ({ default: m.ThirdPartyPluginV130Icon })),
  ),
  ThirdPartyPluginV230Icon: lazy(() =>
    import('../ThirdPartyPluginV230Icon').then(m => ({ default: m.ThirdPartyPluginV230Icon })),
  ),
  ThumbDownHandIcon: lazy(() =>
    import('../ThumbDownHandIcon').then(m => ({ default: m.ThumbDownHandIcon })),
  ),
  ThumbUpHandIcon: lazy(() =>
    import('../ThumbUpHandIcon').then(m => ({ default: m.ThumbUpHandIcon })),
  ),
  Ticket30Icon: lazy(() => import('../Ticket30Icon').then(m => ({ default: m.Ticket30Icon }))),
  TicketIcon: lazy(() => import('../TicketIcon').then(m => ({ default: m.TicketIcon }))),
  TimeIcon: lazy(() => import('../TimeIcon').then(m => ({ default: m.TimeIcon }))),
  TipsAndUpdatesIcon: lazy(() =>
    import('../TipsAndUpdatesIcon').then(m => ({ default: m.TipsAndUpdatesIcon })),
  ),
  TipsIdeaLight24Icon: lazy(() =>
    import('../TipsIdeaLight24Icon').then(m => ({ default: m.TipsIdeaLight24Icon })),
  ),
  TipsIdeaLight30Icon: lazy(() =>
    import('../TipsIdeaLight30Icon').then(m => ({ default: m.TipsIdeaLight30Icon })),
  ),
  UnblockIcon: lazy(() => import('../UnblockIcon').then(m => ({ default: m.UnblockIcon }))),
  UndoIcon: lazy(() => import('../UndoIcon').then(m => ({ default: m.UndoIcon }))),
  Unknown30Icon: lazy(() => import('../Unknown30Icon').then(m => ({ default: m.Unknown30Icon }))),
  UnsubscribeIcon: lazy(() =>
    import('../UnsubscribeIcon').then(m => ({ default: m.UnsubscribeIcon })),
  ),
  UnsubscribersIcon: lazy(() =>
    import('../UnsubscribersIcon').then(m => ({ default: m.UnsubscribersIcon })),
  ),
  UploadIcon: lazy(() => import('../UploadIcon').then(m => ({ default: m.UploadIcon }))),
  UploadMenuIcon: lazy(() =>
    import('../UploadMenuIcon').then(m => ({ default: m.UploadMenuIcon })),
  ),
  UserActivityIcon: lazy(() =>
    import('../UserActivityIcon').then(m => ({ default: m.UserActivityIcon })),
  ),
  UserAttributesIcon: lazy(() =>
    import('../UserAttributesIcon').then(m => ({ default: m.UserAttributesIcon })),
  ),
  UserIcon: lazy(() => import('../UserIcon').then(m => ({ default: m.UserIcon }))),
  ValidateFormatIcon: lazy(() =>
    import('../ValidateFormatIcon').then(m => ({ default: m.ValidateFormatIcon })),
  ),
  VariantPriorityIcon: lazy(() =>
    import('../VariantPriorityIcon').then(m => ({ default: m.VariantPriorityIcon })),
  ),
  VerticalDotsIcon: lazy(() =>
    import('../VerticalDotsIcon').then(m => ({ default: m.VerticalDotsIcon })),
  ),
  ViewColumnIcon: lazy(() =>
    import('../ViewColumnIcon').then(m => ({ default: m.ViewColumnIcon })),
  ),
  ViewDetailsInformationIcon: lazy(() =>
    import('../ViewDetailsInformationIcon').then(m => ({ default: m.ViewDetailsInformationIcon })),
  ),
  VisibilityOffEyeIcon: lazy(() =>
    import('../VisibilityOffEyeIcon').then(m => ({ default: m.VisibilityOffEyeIcon })),
  ),
  Visitor30Icon: lazy(() => import('../Visitor30Icon').then(m => ({ default: m.Visitor30Icon }))),
  VisitorIcon: lazy(() => import('../VisitorIcon').then(m => ({ default: m.VisitorIcon }))),
  VisitorInformationIcon: lazy(() =>
    import('../VisitorInformationIcon').then(m => ({ default: m.VisitorInformationIcon })),
  ),
  WarningIcon: lazy(() => import('../WarningIcon').then(m => ({ default: m.WarningIcon }))),
  WebAssetIcon: lazy(() => import('../WebAssetIcon').then(m => ({ default: m.WebAssetIcon }))),
  WebIcon: lazy(() => import('../WebIcon').then(m => ({ default: m.WebIcon }))),
  WebPersonalizationIcon: lazy(() =>
    import('../WebPersonalizationIcon').then(m => ({ default: m.WebPersonalizationIcon })),
  ),
  WebhookIcon: lazy(() => import('../WebhookIcon').then(m => ({ default: m.WebhookIcon }))),
  WidgetIcon: lazy(() => import('../WidgetIcon').then(m => ({ default: m.WidgetIcon }))),
  WritingAIAssitantIcon: lazy(() =>
    import('../WritingAIAssitantIcon').then(m => ({ default: m.WritingAIAssitantIcon })),
  ),
};
