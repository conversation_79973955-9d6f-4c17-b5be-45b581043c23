{"name": "@antscorp/antsomi-ui", "version": "2.0.86-text-editor-beta.6", "description": "An enterprise-class UI design language and React UI library.", "sideEffects": ["dist/*", "es/**/style/*", "lib/**/style/*", "**/*.css", "**/*.scss", "**/*.less"], "files": ["es", "dist"], "main": "es/index.js", "module": "es/index.js", "unpkg": "dist/index.js", "types": "es/index.d.ts", "scripts": {"prepublishOnly": "yarn && yarn build:es", "build:es": "npm run clean && npm run copy-files && npm run ts-compile", "clean": "<PERSON><PERSON>f dist lib es", "dev": "vite", "build:dist": "webpack --config webpack.build.js", "build": "npm run clean && npm run build:es && npm run build:dist", "eslint": "eslint --ext js,ts,tsx", "lint": "yarn run eslint src", "lint:fix": "yarn run eslint src --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "serve-storybook": "serve storybook-static", "version": "auto-changelog -p && git add CHANGELOG.md", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest run --coverage", "ts-compile": "tsc -p tsconfig.esm.json", "copy-files": "copyfiles -u 1 \"src/**/*.json\" \"src/**/*.svg\" \"src/**/*.png\" \"src/**/*.scss\" es", "icons:export": "sh -e scripts/generate-export-icon.sh", "icons:format": "prettier --loglevel warn --write \"src/components/icons/*.tsx\"", "icons:update": "sh -e scripts/update-icons.sh", "translate:init": "tsx scripts/translate/goolge-sheet-gen.ts"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["yarn lint:fix"]}, "author": "", "license": "MIT", "browserslist": [">0.2%", "not dead", "not op_mini all"], "dependencies": {"@antscorp/antsomi-locales": "1.0.49", "@antscorp/icons": "0.27.56", "@antscorp/image-editor": "1.0.2", "@antscorp/processing-notification": "^1.0.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.1", "@floating-ui/react": "^0.27.8", "@fortawesome/fontawesome-svg-core": "6.1.1", "@fortawesome/free-brands-svg-icons": "6.1.1", "@fortawesome/free-regular-svg-icons": "6.1.1", "@fortawesome/free-solid-svg-icons": "6.1.1", "@fortawesome/react-fontawesome": "0.2.0", "@swiper/core": "^0.0.21", "@tanstack/react-query": "4.20.4", "@tanstack/react-query-devtools": "4.20.4", "@tinymce/tinymce-react": "^3.7.0", "@tiptap/core": "^3.0.1", "@tiptap/extension-color": "^3.0.1", "@tiptap/extension-highlight": "^3.0.1", "@tiptap/extension-link": "^3.0.1", "@tiptap/extension-strike": "^3.0.1", "@tiptap/extension-subscript": "^3.0.1", "@tiptap/extension-superscript": "^3.0.1", "@tiptap/extension-text-align": "^3.0.1", "@tiptap/extension-text-style": "^3.0.1", "@tiptap/extension-typography": "^3.0.1", "@tiptap/extensions": "^3.0.6", "@tiptap/pm": "^3.0.1", "@tiptap/react": "^3.0.1", "@tiptap/starter-kit": "^3.0.1", "@tiptap/suggestion": "^3.0.1", "@types/currency-formatter": "1.5.1", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-frame-component": "^4.1.6", "@types/react-router-dom": "^5.3.3", "@types/react-window": "^1.8.8", "@yaireo/tagify": "^4.31.2", "ace-builds": "1.4.14", "animate.css": "^4.1.1", "antd": "5.12.6", "axios": "^1.4.0", "babel-plugin-file-loader": "^2.0.0", "bignumber.js": "9.1.2", "commander": "^12.1.0", "currency-formatter": "1.5.9", "d3-interpolate": "^3.0.1", "dayjs": "^1.11.10", "deepmerge": "^4.3.1", "fabric": "^5.3.0", "font-awesome": "4.7.0", "he": "^1.2.0", "highlight.js": "^11.8.0", "html-entities": "^2.0.2", "html-react-parser": "^5.1.12", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "i18next": "23.7.9", "i18next-browser-languagedetector": "6.1.2", "immer": "3.0.0", "konva": "^9.3.6", "lodash": "^4.17.21", "moment": "2.29.2", "pako": "2.0.4", "qs": "6.10.3", "quill": "2.0.3", "quill-upload": "0.0.13", "react-ace": "9.5.0", "react-beautiful-dnd": "^13.1.1", "react-color": "2.19.3", "react-cookie": "^7.1.4", "react-custom-scrollbars": "^4.2.1", "react-device-detect": "^2.2.3", "react-draggable": "^4.4.5", "react-fast-compare": "^3.2.1", "react-frame-component": "^5.2.6", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-intersection-observer": "^9.10.3", "react-konva": "^18.2.10", "react-markdown": "^8.0.7", "react-resizable": "^3.0.5", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "rehype-highlight": "^6.0.0", "remark-gfm": "^3.0.1", "remark-gfm-alias-story": "npm:remark-gfm@4.0.0", "socket.io-client": "^4.7.5", "string-replace-to-array": "^2.1.0", "swiper": "^11.0.7", "tinycolor2": "^1.6.0", "tui-image-editor": "^3.15.3", "uniqid": "^5.4.0", "use-context-selector": "^1.4.4", "use-debounce": "^10.0.4", "use-image": "^1.1.1", "use-immer": "^0.4.1", "zustand": "^4.5.2"}, "devDependencies": {"@ant-design/cssinjs": "^1.6.2", "@antscorp/eslint-config-antsomi": "1.0.4", "@babel/cli": "^7.23.4", "@babel/core": "^7.21.3", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.23.4", "@babel/plugin-transform-react-jsx-source": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.23.3", "@commitlint/cli": "17.5.0", "@commitlint/config-conventional": "17.4.4", "@storybook/addon-docs": "^8.6.9", "@storybook/addon-essentials": "^8.6.9", "@storybook/addon-links": "^8.6.9", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-webpack5-compiler-babel": "^3.0.5", "@storybook/blocks": "^8.6.9", "@storybook/manager-api": "^8.6.9", "@storybook/react": "^8.6.9", "@storybook/react-webpack5": "^8.6.9", "@storybook/test": "^8.6.9", "@storybook/theming": "^8.6.9", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/he": "^1.2.3", "@types/jest": "^29.5.0", "@types/lodash": "^4.17.13", "@types/node": "^18.15.10", "@types/pako": "2.0.0", "@types/react": "^18.0.33", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.11", "@types/react-helmet": "^6.1.11", "@types/react-resizable": "^3.0.7", "@types/react-syntax-highlighter": "^15.5.11", "@types/styled-components": "^5.1.26", "@types/tinycolor2": "^1.4.6", "@types/uniqid": "^5.3.4", "@types/yaireo__tagify": "^4.27.0", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "auto-changelog": "^2.4.0", "babel-loader": "^9.1.3", "babel-plugin-import": "^1.13.8", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-styled-components": "^2.1.4", "clsx": "^1.2.1", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "date-fns": "^2.29.3", "esbuild-loader": "^4.0.2", "eslint": "^8.36.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "file-loader": "^6.2.0", "googleapis": "^142.0.0", "husky": "^8.0.3", "jest-styled-components": "^7.2.0", "jsdom": "^24.1.1", "lint-staged": "^13.2.0", "mini-css-extract-plugin": "^2.7.5", "postcss": "^8.4.41", "postcss-loader": "^8.1.1", "prettier": "^3.3.3", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.16.7", "react-router-dom": "5.1.0 || 6.14.2", "sass": "^1.77.8", "sass-loader": "^16.0.1", "storybook": "^8.6.9", "style-loader": "^4.0.0", "styled-components": "^5.3.9", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "tsx": "^4.16.2", "type-fest": "^4.10.2", "typescript": "^5.4.3", "vite": "^5.4.3", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-mkcert": "^1.17.6", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.0.5", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0", "react-router-dom": ">= 5.1.0"}}